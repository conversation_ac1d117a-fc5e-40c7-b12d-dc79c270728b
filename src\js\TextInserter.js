/**
 * 文本插入器
 * 在光标位置插入HTML标签，支持可配置的HTML模板，为扩展性做准备
 */
class TextInserter {
  /**
   * 构造函数
   * @param {HTMLElement} textareaElement - 目标textarea元素
   */
  constructor(textareaElement) {
    if (!textareaElement) {
      throw new Error('TextInserter: textareaElement is required');
    }
    
    this.textarea = textareaElement;
    
    // 可配置的HTML模板，支持扩展性 (需求 4.4, 4.5)
    this.templates = {
      image: '<img src="data\\asset\\{filename}" style="zoom:100%;" />',
      video: '<video src="data\\asset\\{filename}" controls style="max-width:100%;" />',
      audio: '<audio src="data\\asset\\{filename}" controls />'
    };
    
    console.log('TextInserter: Initialized successfully');
  }

  /**
   * 在光标位置插入HTML内容 (需求 1.6)
   * @param {string} htmlContent - 要插入的HTML内容
   */
  insertAtCursor(htmlContent) {
    if (!this.textarea) {
      console.error('TextInserter: textarea element not found');
      return;
    }

    try {
      const startPos = this.textarea.selectionStart;
      const endPos = this.textarea.selectionEnd;
      const textBefore = this.textarea.value.substring(0, startPos);
      const textAfter = this.textarea.value.substring(endPos);

      // 插入HTML内容
      this.textarea.value = textBefore + htmlContent + textAfter;

      // 设置光标位置到插入内容之后
      const newCursorPos = startPos + htmlContent.length;
      this.textarea.setSelectionRange(newCursorPos, newCursorPos);

      // 触发input事件，确保其他监听器能够响应变化
      this.textarea.dispatchEvent(new Event('input', { bubbles: true }));
      
      // 聚焦到textarea
      this.textarea.focus();
      
      console.log(`TextInserter: Successfully inserted content at position ${startPos}`);
    } catch (error) {
      console.error('TextInserter: Error inserting content:', error);
    }
  }

  /**
   * 根据媒体类型和文件名获取插入模板 (需求 4.4, 4.5)
   * @param {string} mediaType - 媒体类型 (image/video/audio)
   * @param {string} filename - 文件名
   * @returns {string} 格式化的HTML标签
   */
  getInsertionTemplate(mediaType, filename) {
    if (!mediaType || !filename) {
      console.error('TextInserter: mediaType and filename are required');
      return '<!-- Error: Missing mediaType or filename -->';
    }

    try {
      const type = mediaType.split('/')[0]; // 从 'image/png' 获取 'image'
      const template = this.templates[type];
      
      if (!template) {
        console.warn(`TextInserter: No template found for media type: ${mediaType}`);
        return `<!-- Unsupported media type: ${mediaType}, filename: ${filename} -->`;
      }

      const result = template.replace('{filename}', filename);
      console.log(`TextInserter: Generated template for ${mediaType}: ${result}`);
      return result;
    } catch (error) {
      console.error('TextInserter: Error generating template:', error);
      return `<!-- Error generating template for ${mediaType} -->`;
    }
  }

  /**
   * 设置自定义模板 (扩展性支持)
   * @param {string} mediaType - 媒体类型
   * @param {string} template - HTML模板，使用{filename}作为占位符
   */
  setTemplate(mediaType, template) {
    if (!mediaType || !template) {
      console.error('TextInserter: mediaType and template are required');
      return;
    }
    
    this.templates[mediaType] = template;
    console.log(`TextInserter: Template set for ${mediaType}: ${template}`);
  }

  /**
   * 获取当前模板
   * @param {string} mediaType - 媒体类型
   * @returns {string} HTML模板
   */
  getTemplate(mediaType) {
    return this.templates[mediaType];
  }

  /**
   * 插入图片标签（便捷方法）
   * @param {string} filename - 图片文件名
   */
  insertImage(filename) {
    if (!filename) {
      console.error('TextInserter: filename is required for insertImage');
      this.insertError('文件名缺失');
      return;
    }
    
    const htmlContent = this.getInsertionTemplate('image', filename);
    this.insertAtCursor(htmlContent);
  }

  /**
   * 插入错误信息
   * @param {string} errorMessage - 错误信息
   */
  insertError(errorMessage) {
    const errorContent = `<!-- Error: ${errorMessage} -->`;
    this.insertAtCursor(errorContent);
    console.log(`TextInserter: Inserted error message: ${errorMessage}`);
  }
  /**
   * 获取所有可用的模板类型
   * @returns {string[]} 模板类型数组
   */
  getAvailableTemplateTypes() {
    return Object.keys(this.templates);
  }

  /**
   * 销毁插入器，清理资源
   */
  destroy() {
    this.textarea = null;
    this.templates = null;
    console.log('TextInserter: Destroyed successfully');
  }
}

// 导出类以供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
  module.exports = TextInserter;
}