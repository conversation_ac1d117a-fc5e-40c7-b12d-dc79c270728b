<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>我的空间</title>
  <link rel="shortcut icon" href="/images/favicon.ico">
  <base target="_blank">
  <link rel="stylesheet" type="text/css" href="/src/css/index.css">
  <link rel="stylesheet" type="text/css" href="/src/css/websiteFrame.css">
  <link rel="stylesheet" type="text/css" href="/node_modules/dragula/dist/dragula.min.css">
  <link rel="stylesheet" type="text/css" href="/src/css/font awesome/all.min.css">
</head>

<body>
  <div id="headMenu">
    <nav id="headMenuNav">
      <ul id="headMenuLeft">
        <li><a href="/" class="thisWebsiteIcon"></a></li>
        <li class="headMenuElement" @mouseenter="openWebFolder()" @mouseleave="closeWebFolder()">
          <a href="webfolder" class="headMenuLink unChoose">网页夹 <i id="webMenuBar" class="fa-solid"
              :class="{'fa-caret-up':webBarIsClose,'fa-caret-down':!webBarIsClose}"></i></a>
          <div id="webFolderDropMenu"
            :style="{opacity:webFolderOpacity,visibility:webFolderVisibility,top:webFolderTop}">
            <ul id="webFolderList1">
              <li class="webFolderItem" @mouseover="webFolderHover(index)" v-for="(webFolderItem,index) in webFolder">
                {{webFolderItem[0].tag}}</li>
            </ul>
            <ul id="webFolderList2">
            </ul>
          </div>
        </li>
        <li class="headMenuElement"><a href="https://kcn74j8y3msl.feishu.cn/wiki/I2TswJ7hQiAbC9khDCtcXlUInrg"
            class="headMenuLink unChoose">个人中心</a></li>
        <li class="headMenuElement"><a href="https://kcn74j8y3msl.feishu.cn/wiki/BiZMwIToAiGhaJkOmp8cbtNfnie" class="headMenuLink unChoose">工作记录</a></li>
        <li class="headMenuElement"><a href="https://weread.qq.com/" class="headMenuLink unChoose">微信读书</a></li>
      </ul>
      <div id="searchCh" class="unChoose none">
        <span id="searchChText" :style="{borderRadius:searchOptionBorderRadius,boxShadow:searchOptionBoxShadow}"
          @mouseenter="openSearchOption()" @mouseleave="closeSearchOption()">{{this.searchCurWeb}}
          <div id="searchChDropMenu"
            :style="{opacity:searchOptionOpacity,visibility:searchOptionVisibility,top:searchOptionTop}">
            <ul>
              <li class="searchChOp" @click="this.searchCurWeb='百度'">百度</li>
              <li class="searchChOp" @click="this.searchCurWeb='谷歌'">谷歌</li>
              <li class="searchChOp" @click="this.searchCurWeb='谷歌学术'">谷歌学术</li>
              <li class="searchChOp" @click="this.searchCurWeb='秘塔'">秘塔</li>
              <li class="searchChOp" @click="this.searchCurWeb='豆包'">豆包</li>
              <li class="searchChOp" @click="this.searchCurWeb='必应'">必应</li>
              <li class="searchChOp" @click="this.searchCurWeb='Github'">Github</li>
            </ul>
          </div>
        </span>
      </div>
      <div class="headMenuSearchForm none" @keydown="headInputKeyCheck"
        :style="{borderRadius:searchBorderRadius,boxShadow:searchBoxShadow}" @mouseenter="openSearchForm()"
        @mouseleave="closeSearchForm()">
        <input id="headMenuSearchInput" placeholder="立即搜索" type="text" maxlength="192" v-model="searchValue">
        <div id="searchWB" class="fa-solid fa-magnifying-glass" @click="searchWebsite()"></div>
        <div id="searchDropMenu" :style="{opacity:searchOpacity,visibility:searchVisibility,top:searchTop}"
          @mouseup.stop="">
          <ul id="searchSuggestList">
            <li v-for="suggestion in this.suggestFolderList"
              @click="this.searchValue ='[网页] '+ suggestion.text;this.searchWebsite();">
              <img class="searchWebIcon" :src=suggestion.d></img><em>{{suggestion.a}}<strong
                  class="searchMatchWord">{{suggestion.b}}</strong>{{suggestion.c}}</em>
            </li>
            <li v-for="suggestion in this.suggestHistoryList"
              @click="this.searchValue =suggestion.text;this.searchWebsite();">
              <strong>[历史] </strong><em>{{suggestion.a}}<strong
                  class="searchMatchWord">{{suggestion.b}}</strong>{{suggestion.c}}</em>
            </li>
            <li v-for="suggestion in this.suggestWebList" @click="this.searchValue = suggestion;this.searchWebsite();">
              {{suggestion}}</li>
          </ul>
          <span class="searchHistory"
            v-if="this.suggestFolderList.length+this.suggestHistoryList.length+this.suggestWebList==0">
            <button v-for="(history,index) in searchHistoryList" class='searchHistoryItem' draggable="true"
              @dragstart="dragItem" type='button' @click='clickSearchHis(history)' :title=history
              @mouseover="this.showHistoryIconList[index]=true" @mouseout="this.showHistoryIconList[index]=false">
              <div class='searchHistoryText'>{{history}}</div>
              <i class='closeIcon fa-solid fa-circle-xmark' :class="{'none':!this.showHistoryIconList[index]}"
                @click.stop="" @mouseup.stop="deleteHistory(index)"></i>
            </button>
            <div class='noHistoryHint' :class="{'none':this.hasHistory}">暂无历史记录</div>
          </span>
          <div id="searchBottomBox">
            <button id="clearHistoryBtn" type="button" @click="clearHistory()"
              v-if="this.suggestFolderList.length+this.suggestHistoryList.length+this.suggestWebList==0">清空搜索历史</button>
            <button id="switchSuggestBtn" type="button"
              @click="switchSuggest()">{{suggestEnable?"关闭云端搜索联想":"启用云端搜索联想"}}</button>
          </div>
        </div>
      </div>
      <span class="unChoose" id="timeText" @mouseenter="enterTimeText()"
        @mouseleave="leaveTimeText()">{{timeText}}</span>
      <span id="calendarDropMenu" :style="{opacity:calendarOpacity,visibility:calendarVisibility,right:calendarPos}"
        @mouseleave="leaveCalendar()">
        <div id="calendarTimeText" :style="{padding:calendarTimePad,fontWeight:calendarTimeSize}">{{timeText}}</div>
        <table id="calendarTable" align="center" border="0" cellpadding="4" cellspacing="0" width="100%">
          <div id="calendarMonthBg" class="unChoose"></div>
          <thead>
            <tr>
              <th>一</th>
              <th>二</th>
              <th>三</th>
              <th>四</th>
              <th>五</th>
              <th>六</th>
              <th>日</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
            </tr>
            <tr>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
            </tr>
            <tr>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
            </tr>
            <tr>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
            </tr>
            <tr>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
            </tr>
            <tr>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
            </tr>
          </tbody>
        </table>
        <div class="moneyBox">
          <div class="moneySpan">{{moneyTozhString}}
            <div id="hcMoneyIcon"></div>
          </div>
          <div class="moneyBtn" @click="moneyHandle(0)">转换</div>
        </div>
        <div class="moneyBox">
          <div class="moneySpan">{{sourceStone}}
            <div id="sourceStoneIcon"></div>
          </div>
          <div class="moneyBtn" @click="moneyHandle(1)">清空</div>
        </div>
      </span>
    </nav>
    <div class="background"></div>
  </div>
  <div class="mainBody">
    <div id="mainBackground"></div>
    <div id="main">
      <div id="window">
        <div id="leftWindow">
          <span id="dailyCard">
            <div id="weather-box">
              <div id="weather-icon" title=""></div>
              <div id="weather-panel">
                <div id="weather-city" onclick="changeCity()"></div>
                <div id="pm25"></div>
                <div id="weatherInfoNow"></div>
              </div>
              <div id="weatherDay"></div>
              <div id="weatherFeel"></div>
              <div id="weatherUV"></div>
            </div>
            <div id="signBtn" onclick="clickSign()">
              <p id="signT"></p>
            </div>
            <div id="weather-futurebox">
              <div id="weather-future">
                <span class="weather-future-item">
                  <span class="weather-future-week">昨天</span>
                  <span class="weather-future-date"></span>
                  <span class="weather-future-day"></span>
                  <span class="weather-future-night"></span>
                </span><span class="weather-future-item">
                  <span class="weather-future-week">今天</span>
                  <span class="weather-future-date"></span>
                  <span class="weather-future-day"></span>
                  <span class="weather-future-night"></span>
                </span><span class="weather-future-item">
                  <span class="weather-future-week">明天</span>
                  <span class="weather-future-date"></span>
                  <span class="weather-future-day"></span>
                  <span class="weather-future-night"></span>
                </span><span class="weather-future-item">
                  <span class="weather-future-week"></span>
                  <span class="weather-future-date"></span>
                  <span class="weather-future-day"></span>
                  <span class="weather-future-night"></span>
                </span><span class="weather-future-item">
                  <span class="weather-future-week"></span>
                  <span class="weather-future-date"></span>
                  <span class="weather-future-day"></span>
                  <span class="weather-future-night"></span>
                </span><span class="weather-future-item">
                  <span class="weather-future-week"></span>
                  <span class="weather-future-date"></span>
                  <span class="weather-future-day"></span>
                  <span class="weather-future-night"></span>
                </span><span class="weather-future-item">
                  <span class="weather-future-week"></span>
                  <span class="weather-future-date"></span>
                  <span class="weather-future-day"></span>
                  <span class="weather-future-night"></span>
                </span><span class="weather-future-item">
                  <span class="weather-future-week"></span>
                  <span class="weather-future-date"></span>
                  <span class="weather-future-day"></span>
                  <span class="weather-future-night"></span>
                </span><span class="weather-future-item">
                  <span class="weather-future-week"></span>
                  <span class="weather-future-date"></span>
                  <span class="weather-future-day"></span>
                  <span class="weather-future-night"></span>
                </span><span class="weather-future-item">
                  <span class="weather-future-week"></span>
                  <span class="weather-future-date"></span>
                  <span class="weather-future-day"></span>
                  <span class="weather-future-night"></span>
                </span>
                <div id="weather-chart"></div>
              </div>
              <div class="weather-bar hide" style="left:0;text-align: left;opacity: 0;" onclick="weather_move(0)">
                &LeftAngleBracket;</div>
              <div class="weather-bar" style="right:0;text-align: right;" onclick="weather_move(1)">&RightAngleBracket;
              </div>
            </div>
          </span>
        </div>
        <div id="middleWindow">
          <div id="mainCard">
            <div id="manageMenu">
              <span id="manage" class="manageBtn far fa-edit" onclick="manageSet()">
              </span><span id="manageExit" class="manageBtn hide far fa-times-circle" onclick="manageExit()">
              </span><span id="noteManageAdd" class="manageBtn hide far fa-plus-square" onclick="noteManageAdd()">
              </span><span id="noteManageHide" class="manageBtn hide fa-regular fa-eye-slash" onclick="noteManageHide(this)">
              </span><span id="dayNightSwitch" style="float:right" class="manageBtn fa-regular"
                onclick="switchDayNight(this)">
              </span>
            </div>
            <div id="noteList"></div>
          </div>
        </div>
        <div id="rightWindow">
          <span id="hotNewCard" class="none">
            <div class="card-header">
              <span id="hotNew-title"></span>
              <span id="hotNew-refresh" class="fa-solid fa-arrows-rotate" onclick="refreshZhNew(true)"></span>
              <div class="card-progress" id="hotNew-progress"></div>
            </div>
            <div id="hotNew-data-wrapper"></div>
          </span>
          <span id="globalNewCard" class="none">
            <div class="card-header">
              <span id="gn-title">GLOBAL NEWS</span>
              <span id="gn-refresh" class="fa-solid fa-arrows-rotate" onclick="refreshGlbNew(true)"></span>
              <div class="card-progress" id="gn-progress"></div>
            </div>
            <div id="gn-data-wrapper"></div>
          </span>
          <span id="themeCard">
            <div id="themeHead">
              <span id="themeTitle" onclick="switchThemeList()"><i class="fas fa-tshirt"></i></span>
              <span id="themeName">
              </span><span id="themeBar" class="fas fa-bars" onclick="switchThemeList()"></span>
            </div>
            <div id="themeList" class="hide unChoose">
              <ul>
                <li class="themeItem" onclick="changeTheme(this)">
                  <span class="tIName">深海 DeepOcean</span>
                  <span class="tIColor" style="background-color: #95d1ff;">
                  </span><span class="tIColor" style="background-color: rgb(0, 121, 174);">
                  </span><span class="tIColor" style="background-color: rgb(0, 74, 118);">
                  </span>
                </li>
                <li class="themeItem" onclick="changeTheme(this)">
                  <span class="tIName">秋庭 AutumnCourt</span>
                  <span class="tIColor" style="background-color: rgb(205, 118, 66);">
                  </span><span class="tIColor" style="background-color: rgb(83, 44, 36);">
                  </span><span class="tIColor" style="background-color: rgb(28, 26, 27);">
                  </span>
                </li>
                <li class="themeItem" onclick="changeTheme(this)">
                  <span class="tIName">幻梦 FantasyDream</span>
                  <span class="tIColor" style="background-color: rgb(110, 119, 244);">
                  </span><span class="tIColor" style="background-color: rgb(204, 208, 237);">
                  </span><span class="tIColor" style="background-color: rgb(112, 112, 143)">
                  </span>
                </li>
              </ul>
            </div>
          </span>
        </div>
      </div>
    </div>
    <div id="signInBg" class="hide">
      <div id="signInWindow">
        <h2 id="SItitle">早安，世界</h2>
        <div class="SIcloseBtn" onclick="closeSign()">✖</div>
        <div class="signBlock">
          <span class="signHead">签到</span><span id="signInStatus">未完成</span>
          <span class="signHead">签退</span><span id="signOutStatus">未完成</span>
        </div>
        <div id="cardBox">
          <div class="card cardMask" onclick="chooseCard(0)">
          </div>
          <div class="card cardMask" onclick="chooseCard(1)">
          </div>
          <div class="card cardMask" onclick="chooseCard(2)">
          </div>
          <div class="card cardMask" onclick="chooseCard(3)">
          </div>
          <div class="card cardMask" onclick="chooseCard(4)">
          </div>
          <div class="card cardMask" onclick="chooseCard(5)">
          </div>
          <div class="card cardMask" onclick="chooseCard(6)">
          </div>
          <div class="card cardMask" onclick="chooseCard(7)">
          </div>
        </div>
        <div id="cardSubmit" onclick="submitCard()">选择</div>
        <p style="font-size:15px">
          <strong>签到与签退</strong>：每日7点后可以签到，21点至24点可以签退，签退可获得【200】合成玉。签退后关闭部分网页功能，直至下次签到开启。
        </p>
        <p style="font-size:15px">
          <br /><strong>卡池明细</strong>：奖池共有8张带有合成玉的签到卡，包括1张【800】，2张【600】，2张【400】，3张【300】。7-7点半：随机使5张签到卡的数额增加300，抽4张，获得数额最大的奖励；7点半-8点：抽4张；8点-8点半：抽2张；8点半-10点：抽1张。
        </p>
      </div>
    </div>
    <div id="footContainer">&copy;2022-2025 zealYOU <span
        style="cursor: pointer;text-decoration: underline;margin-left: 20px;"
        onclick="window.open('https://github.com/zealYOU/homeHTML','_blank')">Github</span>
      <span style="cursor: pointer;text-decoration: underline;margin-left: 10px;"
        onclick="window.open('/log','_blank')">开发日志</span>
    </div>

  </div>
  <script src="/src/js/src/util.js"></script>
  <script src="/src/js/src/vue.global.js"></script>
  <script src="/src/js/src/marked.min.js"></script>
  <script src="/node_modules/pinyin-pro/dist/index.js"></script>
  <script src="/src/js/PinyinCache.js"></script>
  <script src="/src/js/PinyinMatcher.js"></script>
  <script src="/src/js/ClipboardDetector.js"></script>
  <script src="/src/js/MediaUploadHandler.js"></script>
  <script src="/src/js/TextInserter.js"></script>
  <script src="/src/js/websiteFrame.js"></script>
  <script src="/src/js/index.js"></script>
  <script src="/node_modules/dragula/dist/dragula.min.js"></script>
  <script src="/node_modules/echarts/dist/echarts.min.js"></script>
</body>

</html>