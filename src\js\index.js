let noteDrake;
let weatherChart = null;

// 智能粘贴功能管理器
class SmartPasteManager {
  constructor() {
    this.detectors = new Map(); // 存储每个textarea的检测器
    this.uploadHandler = new MediaUploadHandler();
    console.log('SmartPasteManager: Initialized');
  }

  // 为textarea添加智能粘贴功能
  attachToTextarea(textarea) {
    if (!textarea || this.detectors.has(textarea)) {
      return; // 已经附加过或无效元素
    }

    try {
      const detector = new ClipboardDetector(textarea);
      const textInserter = new TextInserter(textarea);

      // 监听媒体检测事件
      textarea.addEventListener('mediaDetected', async (event) => {
        const { file, mediaType, targetElement } = event.detail;

        if (mediaType === 'unsupported') {
          textInserter.insertError('不支持的图片格式');
          return;
        }

        if (!file) {
          textInserter.insertError('无法获取图片文件');
          return;
        }

        // 上传文件
        const result = await this.uploadHandler.uploadMedia(file, mediaType);
        
        if (result.success) {
          // 上传成功，插入图片标签
          textInserter.insertImage(result.filename);
        } else {
          // 上传失败，插入错误信息
          textInserter.insertError(result.error);
        }
      });

      detector.attachPasteListener();
      this.detectors.set(textarea, { detector, textInserter });
      
      console.log('SmartPasteManager: Attached to textarea');
    } catch (error) {
      console.error('SmartPasteManager: Failed to attach to textarea:', error);
    }
  }

  // 从textarea移除智能粘贴功能
  detachFromTextarea(textarea) {
    if (!textarea || !this.detectors.has(textarea)) {
      return;
    }

    try {
      const { detector, textInserter } = this.detectors.get(textarea);
      detector.destroy();
      textInserter.destroy();
      this.detectors.delete(textarea);
      
      console.log('SmartPasteManager: Detached from textarea');
    } catch (error) {
      console.error('SmartPasteManager: Failed to detach from textarea:', error);
    }
  }

  // 销毁管理器
  destroy() {
    for (const [textarea, { detector, textInserter }] of this.detectors) {
      detector.destroy();
      textInserter.destroy();
    }
    this.detectors.clear();
    this.uploadHandler.destroy();
    console.log('SmartPasteManager: Destroyed');
  }
}

// 全局智能粘贴管理器实例
let smartPasteManager = new SmartPasteManager();
/**
 * 主页管理按钮的点击事件
 */
function manageSet() {
  const target = document.getElementById('manage');
  const target1 = document.getElementById('noteManageAdd');
  const target2_1 = document.getElementsByClassName('notePreview');
  const target2_2 = document.getElementsByClassName('noteText');
  const target3 = document.getElementById('manageExit');
  const target4 = document.getElementsByClassName('noteDelete');
  const target5 = document.getElementById('noteManageHide');
  if (target.classList.contains('fa-edit')) {
    target.classList.remove('fa-edit');
    target.classList.add('fa-check-circle');
    target1.classList.remove('hide');
    target3.classList.remove('hide');
    target5.classList.remove('hide');
    document.getElementById('noteList').style.border =
      'dotted var(--ibtnColor) 2px';
    noteDrake = dragula([document.getElementById('noteList')]);
    for (let i = 0; i < target4.length; i++) {
      target4[i].classList.remove('none');
    }
  } else {
    saveNote();
    const target7 = document.getElementsByClassName('textEditing');
    target.classList.remove('fa-check-circle');
    target.classList.add('fa-edit');
    target1.classList.add('hide');
    target3.classList.add('hide');
    target5.classList.add('hide');
    for (let i = 0; i < target2_1.length; i++) {
      if (target2_1[i].classList.contains('none')) {
        renderMarkdown(target2_2[i], target2_1[i]);
        target2_1[i].classList.remove('none');
        target2_2[i].classList.add('none');
      }
    }
    for (let i = target7.length - 1; i >= 0; i--) {
      target7[i].classList.remove('textEditing');
    }
    for (let i = 0; i < target4.length; i++) {
      target4[i].classList.add('none');
    }
    document.getElementById('noteList').style.border = 'dotted transparent 2px';
    noteDrake.destroy();
  }
}

/**
 *从前端获得记录写入本地
 */
function saveNote() {
  let target = document.getElementsByClassName('noteText');
  let tt = '';
  for (let i = 0; i < target.length - 1; i++) {
    tt += target[i].value + '#@##@#';
  }
  if (target.length >= 1) {
    tt += target[target.length - 1].value;
  }
  tt = encodeURIComponent(tt);
  let notehttp = new XMLHttpRequest();
  notehttp.open('POST', 'http://127.0.0.1:6699/post/writenote?' + tt, true);
  notehttp.send();
  notehttp.onreadystatechange = function () {
    if (notehttp.readyState == 4 && notehttp.status != 200) {
      console.info('note本地存储失败');
    }
  };
}

/**
 * 新增一条笔记事项
 */
function noteManageAdd() {
  const tag = `<div class="noteItem textEditing" onmousedown='noteDown(this,event)'><div class="noteDelete" onclick="deleteOnenote(this)">✖</div><div class="notePreview none"></div><textarea class="noteText" onmouseup="noteTextUp(this)"></textarea></div>`;
  document.getElementById('noteList').insertAdjacentHTML('beforeEnd', tag);
  let text = document.getElementById('noteList').lastChild.lastChild;
  text.addEventListener('keydown', event => {
    tabKeyCheck(event, text);
  });
  text.addEventListener('input', event => {
    text.style.height = '50px';
    text.style.height = `${event.target.scrollHeight}px`;
  });
  
  // 为新创建的待办模块textarea添加智能粘贴功能 (需求 1.1, 3.4, 4.2)
  smartPasteManager.attachToTextarea(text);
}

/**
 * 删除指定笔记事项
 */
function deleteOnenote(tar) {
  // 在删除前清理智能粘贴功能
  const textarea = tar.parentElement.querySelector('.noteText');
  if (textarea) {
    smartPasteManager.detachFromTextarea(textarea);
  }
  
  tar.parentElement.remove();
}


/**
 * 显示或隐藏隐私笔记
 */
function noteManageHide(target) {
  if (target.classList.contains('fa-eye-slash')) {
    target.classList.remove('fa-eye-slash');
    target.classList.add('fa-eye');
    // 将所有加了none的笔记显示出来
    const target1 = document.getElementsByClassName('noteItem');
    for (let i = 0; i < target1.length; i++) {
      target1[i].classList.remove('none');
    }
  } else {
    target.classList.remove('fa-eye');
    target.classList.add('fa-eye-slash');
    const target1 = document.getElementsByClassName('noteItem');
    //以hide开头的笔记隐藏
    for (let i = 0; i < target1.length; i++) {
      if (target1[i].getElementsByClassName('noteText')[0].value.startsWith('\\hide')) {
        target1[i].classList.add('none');
      }
    }
  }
}


/**
 * 笔记和日程模块退出管理模式
 */
function manageExit() {
  const target = document.getElementById('manage');
  const target1 = document.getElementById('noteManageAdd');
  const target2 = document.getElementById('noteManageHide');
  const target3 = document.getElementById('manageExit');
  const target4 = document.getElementsByClassName('noteItem');
  const target5 = document.getElementsByClassName('textEditing');
  target.classList.remove('fa-check-circle');
  target.classList.add('fa-edit');
  target1.classList.add('hide');
  target2.classList.add('hide');
  target3.classList.add('hide');
  document.getElementById('noteList').style.border = 'dotted transparent 2px';
  noteDrake.destroy();
  for (i = target5.length - 1; i >= 0; i--)
    target5[i].classList.remove('textEditing');
  
  // 清理智能粘贴功能
  for (i = target4.length - 1; i >= 0; i--) {
    const textarea = target4[i].querySelector('.noteText');
    if (textarea) {
      smartPasteManager.detachFromTextarea(textarea);
    }
    target4[i].remove();
  }
  setNote(); //重新获得记录写入前端
}

/**
 * 从本地存储中获得记录写入前端，并渲染为markdown展示
 */
function setNote() {
  let notehttp = new XMLHttpRequest();
  notehttp.open('POST', 'http://127.0.0.1:6699/post/readnote', true);
  notehttp.send();
  notehttp.onreadystatechange = function () {
    if (notehttp.readyState == 4 && notehttp.status == 200) {
      const ans = decodeURIComponent(notehttp.responseText).split('#@##@#');
      const hideBtn = document.getElementById('noteManageHide');
      for (let i = 0; i < ans.length; i++) {
        let tag = `<div class="noteItem" onmousedown='noteDown(this,event)'><div class="noteDelete none" onclick="deleteOnenote(this)">✖</div><div class="notePreview"></div><textarea class="noteText none" autoHeight="true" onmouseup="noteTextUp(this)"></div></div>`;
        if (hideBtn.classList.contains("fa-eye-slash") && ans[i].startsWith("\\hide")){
          tag = `<div class="noteItem none" onmousedown='noteDown(this,event)'><div class="noteDelete none" onclick="deleteOnenote(this)">✖</div><div class="notePreview"></div><textarea class="noteText none" autoHeight="true" onmouseup="noteTextUp(this)"></div></div>`;
        }
        document
          .getElementById('noteList')
          .insertAdjacentHTML('beforeEnd', tag);
        let text = document.getElementById('noteList').lastChild.lastChild;
        text.addEventListener('keydown', event => {
          tabKeyCheck(event, text);
        });
        text.addEventListener('input', event => {
          text.style.height = '50px';
          text.style.height = `${event.target.scrollHeight}px`;
        });
        
        // 为待办模块的textarea添加智能粘贴功能 (需求 1.1, 3.4, 4.2)
        smartPasteManager.attachToTextarea(text);
      }
      const md = document.getElementsByClassName('notePreview');
      const text = document.getElementsByClassName('noteText');
      for (let i = 0; i < md.length; i++) {
        text[i].value = ans[i];
        renderMarkdown(text[i], md[i]);
      }
    }
  };
}

/**
 * 打开签到签退界面时的初始化
 */
function openSign() {
  let status = localStorage.getItem('signFlag').split('-');
  if (status.length == 4 && status[3] == '1') {
    document.getElementById('signInStatus').innerText =
      localStorage.getItem('signInTime');
    document.getElementById('signOutStatus').innerText = '未完成';
  } else if (status.length == 4 && status[3] == '2') {
    document.getElementById('signInStatus').innerText =
      localStorage.getItem('signInTime');
    document.getElementById('signOutStatus').innerText =
      localStorage.getItem('signOutTime');
  } else if (status.length == 4 && status[3] == '0') {
    document.getElementById('signInStatus').innerText = '未完成';
    document.getElementById('signOutStatus').innerText = '未完成';
  }
  fade_in(document.getElementById('signInBg'));
}

/**
 * 点击签到签退按钮时的响应，若当前可签到或可签退，执行相关操作，
 * 否则通过openSign打开签到签退界面（0-5点不可打开）
 */
function clickSign() {
  const signT = document.getElementById('signT');
  const curT = new Date();
  if (signT.innerText == '单击可签到' && curT.getHours() >= 5) {
    let msg =
      add_zero(curT.getHours()) +
      ':' +
      add_zero(curT.getMinutes()) +
      ':' +
      add_zero(curT.getSeconds());
    let signhttp = new XMLHttpRequest();
    signhttp.open('POST', 'http://127.0.0.1:6699/post/signIn?' + msg, true);
    signhttp.send();
    signhttp.onreadystatechange = function () {
      if (signhttp.readyState == 4 && signhttp.status == 200) {
        localStorage.setItem(
          'signFlag',
          curT.getFullYear() +
          '-' +
          (curT.getMonth() + 1) +
          '-' +
          curT.getDate() +
          '-1'
        );
        localStorage.setItem('signInTime', msg);
        localStorage.setItem('signCardInfo', '0');
        localStorage.setItem('dayNight', 'day');
        location.reload();
      }
    };
  } else if (
    signT.innerText == '单击可签退' &&
    curT.getHours() >= 21 &&
    curT.getHours() <= 23
  ) {
    let msg =
      add_zero(curT.getHours()) +
      ':' +
      add_zero(curT.getMinutes()) +
      ':' +
      add_zero(curT.getSeconds());
    let signhttp = new XMLHttpRequest();
    signhttp.open('POST', 'http://127.0.0.1:6699/post/signOut?' + msg, true);
    signhttp.send();
    signhttp.onreadystatechange = function () {
      if (signhttp.readyState == 4 && signhttp.status == 200) {
        localStorage.setItem(
          'signFlag',
          curT.getFullYear() +
          '-' +
          (curT.getMonth() + 1) +
          '-' +
          curT.getDate() +
          '-2'
        );
        localStorage.setItem('signOutTime', msg);
        if (
          document.getElementById('dayNightSwitch').classList.contains('fa-sun')
        ) {
          switchDayNight(document.getElementById('dayNightSwitch'));
        }
        signT.innerText = '签退：' + msg;
        checkSignOut();
      }
    };
  } else if (curT.getHours() >= 5) {
    openSign();
  }
}

/**
 * 关闭签到签退界面时的响应
 */
function closeSign() {
  fade_out(document.getElementById('signInBg'));
}

let waitSignOut = null;

/**
 * 根据以往记录初始化签到签退模块
 * @param {boolean} isFirst 用于确认是否是初始化，如果不是，说明是周期性检查时间是否到达21点
 *
 */
function setSign(isFirst) {
  const curT = new Date();
  const signM = localStorage.getItem('signFlag');
  const signT = document.getElementById('signT');
  if (isFirst == false && curT.getHours() < 21) {
    return; //时间没到21点，必定不需要更新
  }
  let flag = 0; //检查是否需要初始化抽卡模块
  /*每天的0点至6点禁止签到和签退 */
  if (signM != null) {
    let signs = signM.split('-');
    if (curT.getHours() >= 0 && curT.getHours() <= 6) {
      signT.innerText = '本时段禁止签到';
    } else if (
      signs[0] != curT.getFullYear() ||
      signs[1] != curT.getMonth() + 1 ||
      signs[2] != curT.getDate() ||
      signs[3] == '0'
    ) {
      signT.innerText = '单击可签到';
    } else if (signs[3] == '1' && curT.getHours() >= 21) {
      signT.innerText = '单击可签退';
      flag = 1;
      if (!isFirst) {
        clearInterval(waitSignOut);
      }
    } else if (signs[3] == '1') {
      signT.innerText = '签到：' + localStorage.getItem('signInTime');
      flag = 1;
      if (isFirst) {
        waitSignOut = setInterval(() => {
          setSign(false);
        }, 60000);
      }
    } else if (signs[3] == '2') {
      signT.innerText = '签退：' + localStorage.getItem('signOutTime');
      flag = 1;
    } else signT.innerText = '单击可签到';
  } else {
    if (curT.getHours >= 0 && curT.getHours <= 6) {
      signT.innerText = '本时段禁止签到';
    } else {
      localStorage.setItem(
        'signFlag',
        curT.getFullYear() +
        '-' +
        (curT.getMonth() + 1) +
        '-' +
        curT.getDate() +
        '-0'
      );
      signT.innerText = '单击可签到';
    }
  }
  if (flag && isFirst) {
    if (localStorage.getItem('signCardInfo') == '0') {
      //需要初始化且今日未抽卡
      let hh = parseInt(localStorage.getItem('signInTime').split(':')[0]),
        mm = parseInt(localStorage.getItem('signInTime').split(':')[1]);
      if (hh == 7)
        document.getElementById('cardSubmit').innerText = '确定抽取（0/4）';
      else if (hh == 8 && mm < 30)
        document.getElementById('cardSubmit').innerText = '确定抽取（0/2）';
      else if (hh < 10)
        document.getElementById('cardSubmit').innerText = '确定抽取（0/1）';
      else document.getElementById('cardSubmit').innerText = '无抽奖资格';
    } else {
      //需要初始化且今日已抽卡
      let message = localStorage.getItem('signCardInfo');
      message = message.split(',');
      let maxx = 0;
      for (let i = 0; i < message.length / 2; i++) {
        maxx = Math.max(message[i], maxx);
      }
      document.getElementById('cardSubmit').innerText =
        '获得【' + maxx + '】合成玉';
      let caList = document.getElementsByClassName('card');
      for (let i = message.length / 2; i < message.length; i++) {
        caList[parseInt(message[i])].classList.add('choose');
        caList[parseInt(message[i])].classList.remove('cardMask1');
        caList[parseInt(message[i])].classList.remove('cardMask2');
        caList[parseInt(message[i])].classList.add('cardValue');
        caList[parseInt(message[i])].innerText =
          '' + message[i - message.length / 2] + ' ';
      }
    }
  }
}

/**
 * 抽卡模块点击卡片时的响应
 * @param {number} num 卡片编号
 */
function chooseCard(num) {
  let target = document.getElementsByClassName('card')[num];
  let btn = document.getElementById('cardSubmit');
  let split1 = btn.innerHTML.split('（');
  if (split1.length <= 1) return;
  let split2 = split1[1].split('/');
  if (target.classList.contains('choose')) {
    target.classList.remove('choose');
    btn.innerHTML =
      split1[0] + '（' + (parseInt(split2[0]) - 1).toString() + '/' + split2[1];
  } else if (split1[1][0] != split1[1][2]) {
    target.classList.add('choose');
    btn.innerHTML =
      split1[0] + '（' + (parseInt(split2[0]) + 1).toString() + '/' + split2[1];
  }
}

/**
 * 抽卡模块点击确定按钮时的卡片的动画效果
 * @param {Array} str 选中卡片的下标数组
 * @param {Array} mess 选中卡片对应的积分数组
 */
function signCardAnima(str, mess) {
  const caList = document.getElementsByClassName('card');
  for (let i = 0; i < str.length; i++) {
    caList[str[i]].style.color = 'rgba(200, 70, 95, 0)';
    caList[str[i]].classList.add('turnback');
  }
  let cnt = 0;
  const change = setInterval(() => {
    cnt++;
    if (cnt == 240) {
      clearInterval(change);
    } else if (cnt == 100) {
      for (let i = 0; i < str.length; i++) {
        caList[str[i]].classList.remove('cardMask1');
        caList[str[i]].classList.remove('cardMask2');
        caList[str[i]].classList.add('cardValue');
        caList[str[i]].innerHTML = mess[i];
      }
    } else if (cnt >= 130 && cnt <= 230) {
      const R = parseInt(200.0 + 0.55 * (cnt - 110)),
        G = parseInt(70.0 + 1.85 * (cnt - 110)),
        B = parseInt(95.0 + 1.6 * (cnt - 110));
      for (let i = 0; i < str.length; i++) {
        caList[str[i]].style.color =
          `rgb(${R}, ${G}, ${B}, ${(cnt - 130) / 100.0})`;
      }
    }
  }, 20);
}

/**
 * 处理抽卡模块点击确定按钮时的响应
 */
function submitCard() {
  let btn = document.getElementById('cardSubmit');
  let split1 = btn.innerHTML.split('（');
  if (split1.length <= 1) return;
  if (split1[1][0] == split1[1][2]) {
    let caList = document.getElementsByClassName('card');
    let str = '';
    for (let i = 0; i < caList.length; i++) {
      if (caList[i].classList.contains('choose')) {
        str += i;
      }
    }
    let cardhttp = new XMLHttpRequest();
    cardhttp.open('GET', 'http://127.0.0.1:6699/signCard?' + str);
    cardhttp.send();
    cardhttp.onreadystatechange = () => {
      if (cardhttp.status == 200 && cardhttp.readyState == 4) {
        if (cardhttp.responseText == '') return;
        let message = cardhttp.responseText.split(',');
        let maxx = 0;
        let save = message;
        signCardAnima(str, message);
        setTimeout(() => {
          for (let i = 0; i < message.length; i++)
            maxx = Math.max(message[i], maxx);
          for (let i = 0; i < str.length; i++) save += ',' + str[i];
          localStorage.setItem('signCardInfo', save);
          let HCmoney = parseInt(localStorage.getItem('HCmoney'));
          if (localStorage.getItem('signInTime').split(':')[0] < 7) {
            document.getElementById('cardSubmit').innerHTML =
              '获得【' + (maxx + 300) + '】合成玉';
            HCmoney += maxx + 300;
          } else {
            document.getElementById('cardSubmit').innerHTML =
              '获得【' + maxx + '】合成玉';
            HCmoney += maxx;
          }
          localStorage.setItem('HCmoney', HCmoney);
        }, 5000);
      }
    };
  }
}

/**
 * 将字符串转化为Markdown格式，并进行渲染
 * @param {*} src 要提取字符串的texrarea对象
 * @param {*} obj 要渲染Markdown语法的对象
 */
function renderMarkdown(src, obj) {
  if (src == null || obj == null) return;
  obj.innerHTML = marked.parse(src.value);
}

let lastSelectStart = -1; //为解决在textarea点击无法取消选中的问题，人为判断是否需要取消选中
let lastSelectEnd = -1;

function noteTextUp(obj) {
  if (
    obj.selectionStart == lastSelectStart &&
    obj.selectionEnd == lastSelectEnd
  ) {
    lastSelectStart = -1;
    obj.setSelectionRange(0, 0);
  }
}

/**
 * 鼠标按下笔记事项时的响应，在500ms内连续双击时进入编辑状态，将Markdown展示框隐藏，显示编辑文本框
 * @param {*} obj 指向笔记事项的指针
 */
function noteDown(obj, event) {
  if (document.getElementById('manage').classList.contains('fa-check-circle')) {
    let md = obj.getElementsByClassName('notePreview')[0];
    let text = obj.getElementsByClassName('noteText')[0];
    if (event.button == 2) return;
    if (md.classList.contains('none') == true) {
      let start = text.selectionStart;
      let end = text.selectionEnd;
      if (start != end) {
        lastSelectStart = start;
        lastSelectEnd = end;
      } else {
        lastSelectStart = -1;
      }
      if (obj.classList.contains('mouseDown')) {
        obj.classList.remove('mouseDown');
        obj.classList.remove('textEditing');
        renderMarkdown(text, md);
        text.classList.add('none');
        md.classList.remove('none');
      } else {
        obj.classList.add('mouseDown');
        setTimeout(() => {
          obj.classList.remove('mouseDown');
        }, 500);
      }
    } else {
      if (obj.classList.contains('mouseDown')) {
        obj.classList.remove('mouseDown');
        obj.classList.add('textEditing');
        text.classList.remove('none');
        md.classList.add('none');
        text.style.height = '50px';
        text.style.height = text.scrollHeight + 'px';
      } else {
        obj.classList.add('mouseDown');
        setTimeout(() => {
          obj.classList.remove('mouseDown');
        }, 500);
      }
    }
  }
  return true;
}

/**
 * 检查当前是否签退，如果签退则关闭中文热榜、世界新闻模块。
 * 中文热榜、世界新闻模块只在当前签到后开启，当前签退后关闭
 */
function checkSignOut() {
  let msg = localStorage.getItem('signFlag').split('-');
  //signFlag为空或错误，打开资讯系统、搜索系统
  if (msg.length != 4) {
    zhNewService = new ZhNewModule();
    glbNewService = new GlbNewModule();
    document.getElementById('globalNewCard').classList.remove('none');
    document.getElementById('hotNewCard').classList.remove('none');
    document.getElementById('searchCh').classList.remove('none');
    document
      .getElementsByClassName('headMenuSearchForm')[0]
      .classList.remove('none');
    return true;
  }
  let d = new Date();
  //当前未签到或已签退，关闭资讯系统、搜索系统
  if (msg[2] != d.getDate() || msg[1] != d.getMonth() + 1 || msg[3] != '1') {
    document.getElementById('globalNewCard').classList.add('none');
    document.getElementById('hotNewCard').classList.add('none');
    document.getElementById('searchCh').classList.add('none');
    document
      .getElementsByClassName('headMenuSearchForm')[0]
      .classList.add('none');
    return false;
  }
  //当前已签到，打开所有功能
  zhNewService = new ZhNewModule();
  glbNewService = new GlbNewModule();
  document.getElementById('globalNewCard').classList.remove('none');
  document.getElementById('hotNewCard').classList.remove('none');
  return true;
}

/**
 * 打开关闭主题列表
 */
function switchThemeList() {
  let card = document.getElementById('themeCard');
  let list = document.getElementById('themeList');
  if (list.classList.contains('hide') == true) {
    let cnt = 0;
    list.style.height = '0px';
    fade_in(list);
    let otl = setInterval(() => {
      cnt += 10;
      card.style.height = cnt + 42 + 4 + 'px'; //42为Head的高度，4为margin等间距
      list.style.height = cnt + 'px';
      if (cnt >= 200) {
        clearInterval(otl);
      }
    }, 6);
  } else if (list.style.height == '200px') {
    let cnt = 200;
    fade_out(list);
    let otl2 = setInterval(() => {
      cnt -= 10;
      card.style.height = cnt + 42 + 'px';
      list.style.height = cnt + 'px';
      if (cnt == 0) {
        clearInterval(otl2);
      }
    }, 5);
  }
}

/**
 * 改变网页主题
 * @param {*} target 指向主题列表的指针
 */
function changeTheme(target) {
  let ss = target.getElementsByClassName('tIName')[0].innerHTML;
  let str = ss.split(' ')[1];
  let bef = localStorage.getItem('theme');
  if (bef != str) {
    let list = document.getElementsByTagName('link');
    for (let i = list.length - 1; i >= 0; i--) {
      if (list[i].getAttribute('key') == 'theme') {
        list[i].remove();
      }
    }
    localStorage.setItem('theme', str);
    let new_link = document.createElement('link');
    new_link.setAttribute('key', 'theme');
    new_link.setAttribute('rel', 'stylesheet');
    new_link.setAttribute('type', 'text/css');
    new_link.setAttribute('href', '/src/css/theme/' + str + '.css');
    const dayNight = localStorage.getItem('dayNight');
    if (dayNight == 'night') {
      document.documentElement.setAttribute('theme', `${str}-night`);
    } else if (dayNight == 'day') {
      document.documentElement.setAttribute('theme', `${str}-day`);
    }
    document.body.appendChild(new_link);
    switchThemeList();
    document.getElementById('themeName').innerHTML = ss;
    weatherChart.dispose();
    weatherChart = null;
    getWeather();
  } else {
    switchThemeList();
  }
}

/**
 * 初始化网页主题
 */
function setTheme() {
  let t = localStorage.getItem('theme');
  let list = document.getElementsByClassName('tIName');
  let dayNight = localStorage.getItem('dayNight');
  if (dayNight == 'day') {
    document.getElementById('dayNightSwitch').classList.add('fa-sun');
  } else {
    document.getElementById('dayNightSwitch').classList.add('fa-moon');
  }
  for (let i = 0; i < list.length; i++) {
    if (list[i].innerHTML.split(' ')[1] == t) {
      document.getElementById('themeName').innerText = list[i].innerHTML;
    }
  }
}

/**
 * 更换网页昼夜壁纸
 * @param {*} target 指向昼夜切换按钮的指针
 */
function switchDayNight(target) {
  let status = localStorage.getItem('dayNight');
  let theme = localStorage.getItem('theme');
  if (status == 'day') {
    document.documentElement.setAttribute('theme', `${theme}-night`);
    target.classList.remove('fa-sun');
    target.classList.add('fa-moon');
    localStorage.setItem('dayNight', 'night');
  } else if (status == 'night') {
    document.documentElement.setAttribute('theme', `${theme}-day`);
    localStorage.setItem('dayNight', 'day');
    target.classList.remove('fa-moon');
    target.classList.add('fa-sun');
  }
  weatherChart.dispose();
  weatherChart = null;
  getWeather();
}

class GlbNewModule {
  constructor() {
    this.magazineInfo = [];
    this.EngRanNum = [];
    this.magazineNum = 5;
    this.refreashHandler = -1; //定时刷新进程，国际新闻每30s切换下一秒
    this.refreashLock = false; //国际新闻刷新锁，防止重复刷新
    this.refreshGlbNew();
  }

  /**
   * 初始化或定时随机刷新国际新闻，不从后端更新数据
   * 淡出界面组件-更新数据-淡入界面组件
   */
  setGlbNew() {
    const box = document.getElementById('gn-data-wrapper');
    if (box.children.length == 0) {
      // 初始化情况
      if (this.magazineInfo.length < this.magazineNum) {
        //该条件分支只有在首次初始化时才存在
        for (let i = 0; i < this.magazineInfo.length; i++) {
          let tag =
            '<div class="English-link"><span class="English-link-time">' +
            this.magazineInfo[i][2] +
            '</span><div class="English-info-box"><a class="English-link-info" href="' +
            this.magazineInfo[i][1] +
            '">' +
            this.magazineInfo[i][0] +
            '&nbsp; - <strong style="font-style: italic;">"' +
            this.magazineInfo[i][3] +
            '"</strong></a></div></div>';
          box.insertAdjacentHTML('beforeend', tag);
        }
      } else {
        let number = [];
        let cnt = 0;
        while (cnt < this.magazineNum) {
          let tmp = Math.floor(Math.random() * this.magazineInfo.length);
          if (number.indexOf(tmp) == -1) {
            number.push(tmp);
            cnt++;
          }
        }
        for (let i = 0; i < this.magazineNum; i++) {
          let tag =
            '<div class="English-link" ><span class="English-link-time">' +
            this.magazineInfo[number[i]][2] +
            '</span><div class="English-info-box"><a class="English-link-info" href="' +
            this.magazineInfo[number[i]][1] +
            '">' +
            this.magazineInfo[number[i]][0] +
            '&nbsp; - <strong style="font-style: italic;">"' +
            this.magazineInfo[number[i]][3] +
            '"</strong></a></div></div>';
          box.insertAdjacentHTML('beforeend', tag);
        }
        this.EngRanNum = number;
      }
      this.refreashLock = false;
    } else {
      let outthis = this;
      fade_out(box, 100, () => {
        box.innerHTML = '';
        let number = [];
        let cnt = 0;
        while (cnt < outthis.magazineNum) {
          let tmp = Math.floor(Math.random() * outthis.magazineInfo.length);
          if (number.indexOf(tmp) == -1) {
            number.push(tmp);
            cnt++;
          }
        }
        for (let i = 0; i < outthis.magazineNum; i++) {
          let tag =
            '<div class="English-link" ><span class="English-link-time">' +
            outthis.magazineInfo[number[i]][2] +
            '</span><div class="English-info-box"><a class="English-link-info" href="' +
            outthis.magazineInfo[number[i]][1] +
            '">' +
            outthis.magazineInfo[number[i]][0] +
            '&nbsp; - <strong style="font-style: italic;">"' +
            outthis.magazineInfo[number[i]][3] +
            '"</strong></a></div></div>';
          box.insertAdjacentHTML('beforeend', tag);
        }
        outthis.EngRanNum = number;
        fade_in(box, 350, () => {
          outthis.refreashLock = false;
        });
      });
    }
  }

  /**
   * 点击触发更新国际新闻数据
   * 关闭定时刷新-访问后端-调用setGlbNew()-启动定时刷新
   */
  refreshGlbNew(is_click = false) {
    //访问后端/engnews
    if (this.refreashLock) {
      return;
    }
    this.refreashLock = true;
    const progress = document.getElementById('gn-progress');
    if (this.refreashHandler != -1) {
      clearInterval(this.refreashHandler);
      progress.style.width = '0%';
    }
    //获得上一次更新时间，相隔超过30s访问后端，避免频繁访问
    let updateTime = localStorage.getItem('GlbUpdateTime');
    if (
      this.magazineInfo.length == 0 ||
      updateTime == null ||
      updateTime == '' ||
      Date.now() - updateTime > 30000
    ) {
      localStorage.setItem('GlbUpdateTime', Date.now());
      let glbNewhttp = new XMLHttpRequest();
      glbNewhttp.open('GET', 'http://127.0.0.1:6699/engnews');
      glbNewhttp.send();
      glbNewhttp.onreadystatechange = () => {
        if (glbNewhttp.status == 200 && glbNewhttp.readyState == 4) {
          const EnglishMagazine = JSON.parse(glbNewhttp.responseText);
          this.magazineInfo = []; //每个元素分别有标题、链接、时间、来源
          for (let j = 0; j < EnglishMagazine['list'].length; j++) {
            for (
              let i = 0;
              i < EnglishMagazine['list'][j]['list'].length;
              i++
            ) {
              let data = EnglishMagazine['list'][j]['list'][i]['time'];
              if (data[data.length - 1] == 'd') {
                if (data.length > 2 || data[0] > '5') {
                  continue; //超过5d的新闻不显示
                }
              } else if (
                data[data.length - 1] != 'h' &&
                data[data.length - 1] != 'm'
              ) {
                continue;
              }
              this.magazineInfo.push([
                EnglishMagazine['list'][j]['list'][i]['title'],
                EnglishMagazine['list'][j]['list'][i]['link'],
                data,
                EnglishMagazine['list'][j]['name'],
              ]);
            }
          }
          if (this.magazineInfo.length == 0) {
            document.getElementById('gn-data-wrapper').innerHTML =
              '<div>当前暂无新闻</div>';
            return;
          }
          this.setGlbNew();
          if (this.magazineInfo.length > this.magazineNum) {
            //超过1页才需要刷新
            let cnt = 1;
            if (is_click){
              console.log('click');
              cnt = 0;
              progress.style.width = '0%';
            } else {
              progress.style.width = '10%';
            }
            this.refreashHandler = setInterval(() => {
              cnt++;
              if (cnt == 11) {
                progress.style.width = '0%';
                if (!this.refreashLock) {
                  this.refreashLock = true;
                  this.setGlbNew();
                }
              } else if (cnt == 12){
                cnt = 1;
                progress.style.width = '10%';
              } else {
                progress.style.width = cnt * 10 + '%';
              }
            }, 2400);
          }
        } else if (glbNewhttp.readyState == 4) {
          this.refreashLock = false;
        }
      };
    } else {
      this.setGlbNew();
      let cnt = 1;
      if (is_click){
        cnt = 0;
        progress.style.width = '0%';
      } else {
        progress.style.width = '10%';
      }
      this.refreashHandler = setInterval(() => {
        cnt++;
        if (cnt == 11) {
          progress.style.width = '0%';
          if (!this.refreashLock) {
            this.refreashLock = true;
            this.setGlbNew();
          }
        } else if (cnt == 12){
          cnt = 1;
          progress.style.width = '10%';
        } else {
          progress.style.width = cnt * 10 + '%';
        }
      }, 2400);
    }
  }
}

class ZhNewModule {
  constructor() {
    this.zhNewCurPage = -1; //存储中文热榜当前页码
    this.refreashHandler = -1; //定时刷新进程，中文热榜每30s切换下一秒
    this.refreashLock = false; //中文热榜刷新锁，防止重复刷新
    this.zhNewInfo = [];
    this.refreshZhNew();
  }

  /**
   * 初始化或定时切换中文热榜页码，不从后端更新数据
   * 淡出界面组件-更新数据-淡入界面组件
   */
  setZhNew() {
    const box = document.getElementById('hotNew-data-wrapper');
    const title = document.getElementById('hotNew-title');
    if (this.zhNewCurPage == -1) {
      this.zhNewCurPage = 0;
      title.innerText =
        this.zhNewInfo[this.zhNewCurPage]['name'] +
        ` (${this.zhNewCurPage + 1}/${this.zhNewInfo.length})`;
      box.insertAdjacentHTML(
        'beforeend',
        "<div class='hnItem'><span class='hnIndex' style='color:rgb(255,50,50)'>1</span><span class='hnTCon'><a class='hnText' href='" +
        this.zhNewInfo[this.zhNewCurPage]['list'][0]['link'] +
        "'>" +
        this.zhNewInfo[this.zhNewCurPage]['list'][0]['title'] +
        '</a></span></div>'
      );
      box.insertAdjacentHTML(
        'beforeend',
        "<div class='hnItem'><span class='hnIndex' style='color:rgb(255,155,60)'>2</span><span class='hnTCon'><a class='hnText' href='" +
        this.zhNewInfo[this.zhNewCurPage]['list'][1]['link'] +
        "'>" +
        this.zhNewInfo[this.zhNewCurPage]['list'][1]['title'] +
        '</a></span></div>'
      );
      box.insertAdjacentHTML(
        'beforeend',
        "<div class='hnItem'><span class='hnIndex' style='color:rgb(245,200,20)'>3</span><span class='hnTCon'><a class='hnText' href='" +
        this.zhNewInfo[this.zhNewCurPage]['list'][2]['link'] +
        "'>" +
        this.zhNewInfo[this.zhNewCurPage]['list'][2]['title'] +
        '</a></span></div>'
      );
      for (
        let i = 3;
        i < Math.min(10, this.zhNewInfo[this.zhNewCurPage]['list'].length);
        i++
      ) {
        box.insertAdjacentHTML(
          'beforeend',
          "<div class='hnItem'><span class='hnIndex'>" +
          (i + 1) +
          "</span><span class='hnTCon'><a class='hnText' href='" +
          this.zhNewInfo[this.zhNewCurPage]['list'][i]['link'] +
          "'>" +
          this.zhNewInfo[this.zhNewCurPage]['list'][i]['title'] +
          '</a></span></div>'
        );
      }
      this.refreashLock = false;
    } else {
      this.zhNewCurPage = (this.zhNewCurPage + 1) % this.zhNewInfo.length;
      let outthis = this;
      fade_out(box, 100);
      fade_out(title, 100, () => {
        box.innerHTML = '';
        title.innerText =
          this.zhNewInfo[this.zhNewCurPage]['name'] +
          ` (${this.zhNewCurPage + 1}/${this.zhNewInfo.length})`;
        box.insertAdjacentHTML(
          'beforeend',
          "<div class='hnItem'><span class='hnIndex' style='color:rgb(255,50,50)'>1</span><span class='hnTCon'><a class='hnText' href='" +
          this.zhNewInfo[this.zhNewCurPage]['list'][0]['link'] +
          "'>" +
          this.zhNewInfo[this.zhNewCurPage]['list'][0]['title'] +
          '</a></span></div>'
        );
        box.insertAdjacentHTML(
          'beforeend',
          "<div class='hnItem'><span class='hnIndex' style='color:rgb(255,155,60)'>2</span><span class='hnTCon'><a class='hnText' href='" +
          this.zhNewInfo[this.zhNewCurPage]['list'][1]['link'] +
          "'>" +
          this.zhNewInfo[this.zhNewCurPage]['list'][1]['title'] +
          '</a></span></div>'
        );
        box.insertAdjacentHTML(
          'beforeend',
          "<div class='hnItem'><span class='hnIndex' style='color:rgb(245,200,20)'>3</span><span class='hnTCon'><a class='hnText' href='" +
          this.zhNewInfo[this.zhNewCurPage]['list'][2]['link'] +
          "'>" +
          this.zhNewInfo[this.zhNewCurPage]['list'][2]['title'] +
          '</a></span></div>'
        );
        for (
          let i = 3;
          i < Math.min(10, this.zhNewInfo[this.zhNewCurPage]['list'].length);
          i++
        ) {
          box.insertAdjacentHTML(
            'beforeend',
            "<div class='hnItem'><span class='hnIndex'>" +
            (i + 1) +
            "</span><span class='hnTCon'><a class='hnText' href='" +
            this.zhNewInfo[this.zhNewCurPage]['list'][i]['link'] +
            "'>" +
            this.zhNewInfo[this.zhNewCurPage]['list'][i]['title'] +
            '</a></span></div>'
          );
        }
        fade_in(title, 350);
        fade_in(box, 350, () => {
          outthis.refreashLock = false;
        });
      });
    }
  }
  /**
   * 点击触发更新中文热榜数据
   * 关闭定时刷新-访问后端-调用setZhNew()-启动定时刷新
   */
  refreshZhNew(is_click = false) {
    //访问后端/zhnew
    if (this.refreashLock) {
      return;
    }
    this.refreashLock = true;
    const progress = document.getElementById('hotNew-progress');
    if (this.refreashHandler != -1) {
      clearInterval(this.refreashHandler);
      progress.style.width = '0%';
    }
    //获得上一次更新时间，相隔超过30s访问后端，避免频繁访问
    let updateTime = localStorage.getItem('ZhUpdateTime');
    if (
      this.zhNewInfo.length == 0 ||
      updateTime == null ||
      updateTime == '' ||
      Date.now() - updateTime > 30000
    ) {
      localStorage.setItem('ZhUpdateTime', Date.now());
      let zhNewhttp = new XMLHttpRequest();
      zhNewhttp.open('GET', 'http://127.0.0.1:6699/zhnew');
      zhNewhttp.send();
      zhNewhttp.onreadystatechange = () => {
        if (zhNewhttp.status == 200 && zhNewhttp.readyState == 4) {
          if (zhNewhttp.responseText == '') {
            document
              .getElementById('hotNew-data-wrapper')
              .insertAdjacentHTML('beforeend', '<div>后端无数据</div>');
            this.refreashLock = false;
            return;
          }
          let rawData = JSON.parse(zhNewhttp.responseText)['list'];
          let new_list = [];
          for (let i = 0; i < rawData.length; i++) {
            if (rawData[i]['list'].length >= 3) {
              new_list.push(rawData[i]);
            }
          }
          this.zhNewInfo = new_list;
          this.setZhNew();
          let cnt = 1;
          if (is_click){
            cnt = 0;
            progress.style.width = '0%';
          } else {
            progress.style.width = '10%';
          }
          this.refreashHandler = setInterval(() => {
            cnt++;
            if (cnt == 11) {
              progress.style.width = '0%';
              if (!this.refreashLock) {
                this.refreashLock = true;
                this.setZhNew();
              }
            } else if (cnt == 12){
              cnt = 1;
              progress.style.width = '10%';
            } else {
              progress.style.width = cnt * 10 + '%';
            }
          },2400);
        } else if (zhNewhttp.readyState == 4) {
          this.refreashLock = false;
        }
      };
    } else {
      this.setZhNew();
      let cnt = 1;
      if (is_click){
        cnt = 0;
        progress.style.width = '0%';
      } else {
        progress.style.width = '10%';
      }
      this.refreashHandler = setInterval(() => {
        cnt++;
        if (cnt == 11) {
          progress.style.width = '0%';
          if (!this.refreashLock) {
            this.refreashLock = true;
            this.setZhNew();
          }
        } else if (cnt == 12){
          cnt = 1;
          progress.style.width = '10%';
        } else {
          progress.style.width = cnt * 10 + '%';
        }
      }, 2400);
    }
  }
}

let zhNewService;
let glbNewService;

function refreshZhNew(is_click = false) {
  if (zhNewService != null) {
    zhNewService.refreshZhNew(is_click);
  }
}

function refreshGlbNew(is_click = false) {
  if (glbNewService != null) {
    glbNewService.refreshGlbNew(is_click);
  }
}

/**
 * 对tab键处理，输入tab插入制表符
 */
function tabKeyCheck(event, obj) {
  if (event.key === 'Tab') {
    event.preventDefault();
    console.info(obj);
    const start = obj.selectionStart;
    const end = obj.selectionEnd;
    const value = obj.value;
    obj.value = value.substring(0, start) + '    ' + value.substring(end);
    obj.selectionStart = obj.selectionEnd = start + 4; // 将光标定位到插入制表符后
  }
}

/**
 * name 天气名， time 1为白天，0为夜晚
 */
function getWeatherIconPath(name, time) {
  let iconPath = '';
  switch (name) {
    case '多云':
      if (time == 1) iconPath = '../images/weather/duoyun.svg';
      else iconPath = '../images/weather/duoyunye.svg';
      break;
    case '晴':
      if (time == 1) iconPath = '../images/weather/qing.svg';
      else iconPath = '../images/weather/qingye.svg';
      break;
    case '阵雪':
      if (time == 1) iconPath = '../images/weather/zhenxue.svg';
      else iconPath = '../images/weather/zhenxueye.svg';
      break;
    case '雨':
    case '小雨':
      iconPath = '../images/weather/xiaoyu.svg';
      break;
    case '小雪':
      iconPath = '../images/weather/xiaoxue.svg';
      break;
    case '小到中雨':
    case '中雨':
      iconPath = '../images/weather/zhongyu.svg';
      break;
    case '小到中雪':
    case '中雪':
      iconPath = '../images/weather/zhongxue.svg';
      break;
    case '阴':
      iconPath = '../images/weather/yin.svg';
      break;
    case '中到大雨':
    case '大雨':
      iconPath = '../images/weather/dayu.svg';
      break;
    case '大到暴雨':
    case '暴雨':
      iconPath = '../images/weather/baoyu.svg';
      break;
    case '大暴雨':
      iconPath = '../images/weather/dabaoyu.svg';
      break;
    case '特大暴雨':
      iconPath = '../images/weather/tedabaoyu.svg';
      break;
    case '中到大雪':
    case '大雪':
      iconPath = '../images/weather/daxue.svg';
      break;
    case '雨夹雪':
      iconPath = '../images/weather/yujiaxue.svg';
      break;
    case '阵雨':
      iconPath = '../images/weather/zhenyu.svg';
      break;
    case '雷阵雨':
      iconPath = '../images/weather/leizhenyu.svg';
      break;
    case '沙尘暴':
      iconPath = '../images/weather/shachenbao.svg';
      break;
    case '强沙尘暴':
      iconPath = '../images/weather/qiangshachenbao.svg';
      break;
    case '霾':
    case '大霾':
      iconPath = '../images/weather/mai.svg';
      break;
    case '浮尘':
      iconPath = '../images/weather/fuchen.svg';
      break;
    case '扬沙':
      iconPath = '../images/weather/yangsha.svg';
      break;
    case '雾':
    case '大雾':
      iconPath = '../images/weather/wu.svg';
      break;
    case '雷阵雨伴冰雹':
      iconPath = '../images/weather/leizhenyubanbingbao.svg';
      break;
    default:
      console.error('未知天气：', name);
  }
  return iconPath;
}

/**
 * 获取天气信息
 * @param {*} city 城市名
 */
function updateWeather(weatherInfo) {
  function fetchWeatherIcon(path, element) {
    fetch(path)
      .then(response => {
        if (!response.ok) {
          throw new Error('Failed to fetch weather icon');
        }
        return response.text();
      })
      .then(data => {
        let parser = new DOMParser();
        let svg = parser.parseFromString(data, 'image/svg+xml').documentElement;
        element.appendChild(svg);
      })
      .catch(error => {
        console.error('Failed to fetch weather icon:', error);
        // 可以考虑添加默认图标或错误提示
      });
  }
  const pm25 = document.getElementById('pm25');
  const ico = document.getElementById('weather-icon');
  while (pm25.classList.length > 0) {
    pm25.classList.remove(pm25.classList[0]);
  }
  if (weatherInfo.pm25 < 50) {
    pm25.innerText = `优 ${weatherInfo.pm25}`;
    pm25.classList.add('pm25_bg1');
  } else if (weatherInfo.pm25 < 100) {
    pm25.innerText = `良 ${weatherInfo.pm25}`;
    pm25.classList.add('pm25_bg2');
  } else if (weatherInfo.pm25 < 150) {
    pm25.innerText = `轻度 ${weatherInfo.pm25}`;
    pm25.classList.add('pm25_bg3');
  } else if (weatherInfo.pm25 < 200) {
    pm25.innerText = `中度 ${weatherInfo.pm25}`;
    pm25.classList.add('pm25_bg4');
  } else if (weatherInfo.pm25 < 300) {
    pm25.innerText = `重度 ${weatherInfo.pm25}`;
    pm25.classList.add('pm25_bg5');
  } else {
    pm25.innerText = `严重 ${weatherInfo.pm25}`;
    pm25.classList.add('pm25_bg6');
  }
  let updateNow = false; //当城市改变时，需要立即更新图表
  if (document.getElementById('weather-city').innerText != weatherInfo.city) {
    document.getElementById('weather-city').innerText = `${weatherInfo.city}`;
    updateNow = true;
  }
  document.getElementById('weatherInfoNow').innerHTML =
    `<span id="temperatureNow">${weatherInfo.temperature}°</span> <span id="weatherNow">${weatherInfo.weather}</span>`;
  document.getElementById('weatherFeel').innerText =
    `体感：${weatherInfo.bodytemp}`;
  document.getElementById('weatherUV').innerText = `紫外线：${weatherInfo.uv}`;
  const hour = new Date().getHours();
  let iconPath = '';
  let wind_level = ''; //wind_level存储当前风力
  if (hour >= 6 && hour < 18) {
    iconPath = getWeatherIconPath(weatherInfo.weather, 1);
    wind_level = weatherInfo.wind_power_day;
  } else {
    iconPath = getWeatherIconPath(weatherInfo.weather, 0);
    wind_level = weatherInfo.wind_power_night;
  }
  document.getElementById('weatherDay').innerText =
    `今日${weatherInfo.weather_day}，${weatherInfo.temperature_night}°~${weatherInfo.temperature_day}°，${weatherInfo.wind_direction_day}${wind_level}`;
  ico.title = `数据更新于${weatherInfo.time}`;

  if (ico.children.length > 0) {
    ico.removeChild(ico.children[0]);
  }
  fetchWeatherIcon(iconPath, ico);
  if (updateNow || weatherChart == null) {
    if (weatherChart != null) {
      weatherChart.dispose();
      weatherChart = null;
    }
    const weatherWeek = document.getElementsByClassName('weather-future-week');
    const weatherDate = document.getElementsByClassName('weather-future-date');
    const weatherDay = document.getElementsByClassName('weather-future-day');
    const weatherNight = document.getElementsByClassName(
      'weather-future-night'
    );
    let curDate = new Date();
    const week_num = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
    for (let i = 3; i < weatherWeek.length; i++) {
      weatherWeek[i].innerText = week_num[(curDate.getDay() + i - 1) % 7];
    }
    curDate.setDate(curDate.getDate() - 1);
    let maxTemp = -1000,
      minTemp = 1000;
    for (let i = 0; i < weatherInfo.days_list.length; i++) {
      maxTemp = Math.max(
        maxTemp,
        parseInt(weatherInfo.days_list[i].temperature_day)
      );
      minTemp = Math.min(
        minTemp,
        parseInt(weatherInfo.days_list[i].temperature_night)
      );
    }
    let daydc = new Array(weatherInfo.days_list.length).fill(0); //将数值映射到[0,20]，确保曲线图相对好看
    let nightdc = new Array(weatherInfo.days_list.length).fill(0);
    for (let i = 0; i < weatherInfo.days_list.length; i++) {
      daydc[i] =
        20 -
        (20 * (maxTemp - weatherInfo.days_list[i].temperature_day)) /
        (maxTemp - minTemp);
      nightdc[i] =
        (20 * (weatherInfo.days_list[i].temperature_night - minTemp)) /
        (maxTemp - minTemp);
    }
    for (let i = 0; i < weatherInfo.days_list.length; i++) {
      weatherDate[i].innerText =
        `${add_zero(curDate.getMonth() + 1)}-${add_zero(curDate.getDate())}`;
      curDate.setDate(curDate.getDate() + 1);
      weatherDay[i].innerHTML =
        `<div style="position: absolute;width:100%;top:${7 + (95 * (20 - daydc[i])) / 20}px">${weatherInfo.days_list[i].temperature_day}°</div>`;
      weatherNight[i].innerHTML =
        `<div style="position: absolute;width:100%;top:${-7 - (95 * nightdc[i]) / 20}px">${weatherInfo.days_list[i].temperature_night}°</div>`;
      let iconPath_day = getWeatherIconPath(
        weatherInfo.days_list[i].weather_day,
        1
      );
      let iconPath_night = getWeatherIconPath(
        weatherInfo.days_list[i].weather_night,
        0
      );
      weatherDay[i].title = weatherInfo.days_list[i].weather_day;
      weatherNight[i].title = weatherInfo.days_list[i].weather_night;
      fetchWeatherIcon(iconPath_day, weatherDay[i]);
      fetchWeatherIcon(iconPath_night, weatherNight[i]);
    }
    createWeatherChart(daydc, nightdc);
  }
}

function weather_move(direction) {
  const weatherfuture = document.getElementById('weather-future');
  if (direction == 0) {
    //将表格的style的left参数设置为-100%
    let cnt = 0;
    fade_in(document.getElementsByClassName('weather-bar')[1], 200);
    fade_out(document.getElementsByClassName('weather-bar')[0], 0);
    let weather_right_lock = setInterval(() => {
      cnt++;
      if (cnt == 45) clearInterval(weather_right_lock);
      weatherfuture.style.left = -100 * easeInOut(1 - cnt / 45) + '%';
    }, 16);
  } else if (direction == 1) {
    //将表格的style的left参数设置为0
    let cnt = 0;
    fade_in(document.getElementsByClassName('weather-bar')[0], 200);
    fade_out(document.getElementsByClassName('weather-bar')[1], 0);
    let weather_left_lock = setInterval(() => {
      cnt++;
      if (cnt == 45) clearInterval(weather_left_lock);
      weatherfuture.style.left = -100 * easeInOut(cnt / 45) + '%';
    }, 16);
  }
}

/**
 * 修改当前所在城市，从而修改天气信息
 */
function changeCity() {
  //网页弹框，输入城市名
  let cityName = prompt(
    '请输入所在城市名（形如：杭州市/西湖区/杭州市西湖区）：'
  );
  if (
    cityName == null ||
    cityName == '' ||
    cityName.trim() == document.getElementById('weather-city').innerText
  ) {
    return;
  }
  cityName = cityName.trim();
  let http = new XMLHttpRequest();
  http.open('GET', '/setcity?' + encodeURIComponent(cityName));
  http.onreadystatechange = () => {
    if (http.status == 200 && http.readyState == 4) {
      if (http.responseText != 'error')
        updateWeather(JSON.parse(http.responseText));
    }
  };
  http.send();
}

/**
 * 从后端获取天气信息 /weather
 */
function getWeather() {
  let weatherhttp = new XMLHttpRequest();
  weatherhttp.open('get', '/weather');
  weatherhttp.onreadystatechange = () => {
    if (weatherhttp.status == 200 && weatherhttp.readyState == 4) {
      updateWeather(JSON.parse(weatherhttp.responseText));
    }
  };
  weatherhttp.send();
}

function createWeatherChart(daydc, nightdc) {
  let defaultColor = window.getComputedStyle(
    document.getElementById('temperatureNow')
  ).color;
  let themeColor = window.getComputedStyle(
    document.getElementById('weather-icon')
  ).color;
  if (themeColor.startsWith('rgb(')) {
    document.getElementsByClassName('weather-future-item')[1].style.background =
      `linear-gradient( rgba(${themeColor.substring(4, themeColor.length - 1)},0.05), rgba(${themeColor.substring(4, themeColor.length - 1)},0.15),rgba(${themeColor.substring(4, themeColor.length - 1)},0.05))`;
  } else if (themeColor.startsWith('#')) {
    document.getElementsByClassName('weather-future-item')[1].style.background =
      `linear-gradient( ${themeColor}0D, ${themeColor}26,${themeColor}0D)`;
  } else if (themeColor.startsWith('rgba(')) {
    document.getElementsByClassName('weather-future-item')[1].style.background =
      `linear-gradient( rgba(${themeColor.substring(5, themeColor.length - 1)},0.05), rgba(${themeColor.substring(5, themeColor.length - 1)},0.15),rgba(${themeColor.substring(5, themeColor.length - 1)},0.05))`;
  }
  weatherChart = echarts.init(document.getElementById('weather-chart'));
  option = {
    xAxis: {
      type: 'category',
      data: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10'],
      show: false,
    },
    yAxis: {
      type: 'value',
      show: false,
      min: null,
      max: null,
    },
    grid: {
      left: 0,
      right: 0,
      top: '8px',
      bottom: '12px',
    },
    series: [
      {
        data: [
          daydc[0],
          daydc[1],
          daydc[2],
          daydc[3],
          daydc[4],
          daydc[5],
          daydc[6],
          daydc[7],
          daydc[8],
          daydc[9],
        ],
        type: 'line',
        smooth: true,
        fontSize: 15,
        symbolSize: 8,
        lineStyle: {
          color: themeColor,
          type: 'dashed',
          opacity: 0.7,
          width: 1,
        },
        itemStyle: {
          color: defaultColor,
          opacity: 0.9,
        },
        emphasis: {
          itemStyle: {
            color: themeColor,
            opacity: 1,
          },
        },
      },
      {
        data: [
          nightdc[0],
          nightdc[1],
          nightdc[2],
          nightdc[3],
          nightdc[4],
          nightdc[5],
          nightdc[6],
          nightdc[7],
          nightdc[8],
          nightdc[9],
        ],
        type: 'line',
        smooth: true,
        symbolSize: 8,
        lineStyle: {
          color: themeColor,
          type: 'dashed',
          opacity: 0.7,
          width: 1,
        },
        itemStyle: {
          color: defaultColor,
          opacity: 0.9,
        },
        emphasis: {
          itemStyle: {
            color: themeColor,
            opacity: 1,
          },
        },
      },
    ],
  };
  weatherChart.setOption(option);
  window.addEventListener('resize', () => {
    weatherChart.resize();
  });
}

/**
 * 初始化页面的主函数
 */
function main() {
  setTheme();
  setSign(true);
  getWeather();
  setInterval(() => {
    getWeather();
  }, 300000);
  setNote();
  checkSignOut();
}

window.onload = main;

// 页面卸载时清理智能粘贴功能，防止内存泄漏
window.addEventListener('beforeunload', () => {
  if (smartPasteManager) {
    smartPasteManager.destroy();
  }
});