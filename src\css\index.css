body {
  min-width: 1080px;
  overflow: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
  font-family: "Arial";
}

body::-webkit-scrollbar {
  display: none;
}

textarea {
  resize: none;
}

.hide {
  visibility: hidden;
}

.mainBody {
  background-color: rgb(206, 231, 251);
  min-height: calc(1460px + 5vh);
  width: auto;
}

#mainBackground {
  z-index: 0;
  position: fixed;
  top: 0;
  left: 0%;
  width: 100%;
  background: var(--back);
  background-size: var(--bgSize1);
  height: 100%;
}

#main {
  height: calc(1400px + 5vh);
  max-width: 1800px;
  margin: 0 auto;
}

#window {
  width: 97%;
  height: calc(100% - 75px);
  padding: 65px 1.5% 10px 1.5%;
  display: flex;
}

#leftWindow {
  flex: 19;
}

#middleWindow {
  height: 100%;
  z-index: 10;
  margin:0 1.4%;
  flex:54;
}

.timeInput {
  display: inline-block;
  width: 100px;
  height: 40px;
  margin: 10px 15px 5px 15px;
}

#rightWindow {
  flex:20;
}

#mainCard {
  background: var(--theme);
  background-size: var(--themeSize);
  height: 95%;
  padding: 8px 10px 15px 10px;
  background-color: var(--blockBackC);
  opacity: 1;
  border-radius: 25px;
}

.noteItem {
  display: block;
  margin: 0 8px 13px 8px;
  min-height: 32px;
  background: var(--divBack);
  border-radius: 20px 6px 6px 20px;
  border: var(--noteBor);
  cursor: default;
  position: relative;
}

.noteItem:hover {
  border: var(--noteBor2);
}

.noteDelete {
  position: absolute;
  right: 4px;
  top: 4px;
  height: 20px;
  line-height: 20px;
  font-size: 20px;
  color: var(--ibtnColor);
  -webkit-transition: all 0.15s ease-in-out;
  -moz-transition: all 0.15s ease-in-out;
  -ms-transition: all 0.15s ease-in-out;
  -o-transition: all 0.15s ease-in-out;
  transition: all 0.15s ease-in-out;
  cursor: pointer;
  opacity: 0.6;
}

.noteItem:hover .noteDelete {
  opacity: 1;
}

.noteDelete:hover {
  color: var(--ibtnHoverColor);
}

.noteTitle {
  font-size: 23px;
  font-weight: bolder;
  text-align: center;
  display: block;
}

.noteText {
  width: calc(100% - 36px);
  min-height: 50px;
  font-size: 15px;
  padding: 8px 16px 0 20px;
  border-radius: 25px 6px 6px 25px;
}

.notePreview {
  background-color: transparent;
  min-width: 100px;
  line-height: 28px;
  padding: 5px 12px 4px 15px;
  border-radius: 25px 6px 6px 25px;
  font-family: "Arial", sans-serif;
  overflow-x: hidden;
  word-wrap: break-word;
  word-break: break-all;
  overflow-wrap: break-word;
  box-sizing: border-box;
}

.notePreview p {
  white-space: pre-wrap;
}

.notePreview ol {
  padding-inline-start: 32px;
}

.notePreview ul {
  padding-inline-start: 32px;
}

.notePreview ul > li {
  list-style-type: disc;
  white-space: pre-wrap;
}

.notePreview ol > li {
  list-style-type: decimal;
  white-space: pre-wrap;
}

.notePreview hr {
  height: 2px;
  padding: 0;
  margin: 16px 0;
  background: var(--horizontal-divider-color);
  border: 0 none;
}

.notePreview table {
  border-collapse: collapse;
  border-spacing: 0;
  width: calc(100% - 40px);
  margin: 8px 20px;
  text-align: left;
}

.notePreview table td {
  min-width: 32px;
  border: var(--table-border-color);
  padding: 4px 10px;
  white-space: pre-wrap;
}

.notePreview table th {
  min-width: 32px;
  border: var(--table-border-color);
  padding: 5px 10px;
  white-space: pre-wrap;
}

.notePreview a {
  color: var(--defaultColor);
  text-decoration: underline;
}

.notePreview a:hover {
  text-decoration: underline;
  color: var(--themeColor);
}

.notePreview kbd {
  border: 0.1em solid #5b5b5e;
  background: transparent;
  color: var(--text-color);
  margin: 0 0.4rem;
  font-size: 0.95rem;
  padding: 0.3em 0.4em;
  border-radius: 0.4em;
  vertical-align: top;
  box-shadow: 0.1em 0.1em 0.2em rgb(0 0 0 / 30%);
}

.notePreview h1 {
  font-size: 25px;
  margin: 7px 0 5px 0;
  font-family:"Calibri","楷体";
  text-align: center;
}

.notePreview h2 {
  font-size: 22px;
  margin: 6px 0 3px 0;
  font-family:"Calibri","楷体";
}

.notePreview h3 {
  font-size: 18px;
  margin: 4px 0 2px 0;
  font-family:"Calibri","宋体";
  line-height: 26px;
}

.notePreview h4 {
  font-size: 18px;
  margin: 2px 0 0px 0;
  font-family:"Calibri","宋体";
  line-height: 24px;
  font-weight: normal;
}

.notePreview h5 {
  font-size: 16px;
  font-weight: normal;
}

.notePreview h6 {
  font-size: 16px;
  font-weight: normal;
}

pre.code {
  display: table;
  table-layout: fixed;
  width: calc(100% - 6px);
  white-space: pre-wrap;
  background-color: rgba(31, 34, 42, 0.85);
  color: rgb(173, 178, 191);
  padding: 6px 6px 6px 0;
  border-radius: 6px;
  line-height: 19px;
  font-size: 14px;
}
pre.code code {
  font-family: "Consolas", "Arial";
  letter-spacing: 1px;
  display: table-cell;
}
pre.code::before {
  counter-reset: linenum;
}
pre.code span.tr {
  display: table-row;
  counter-increment: linenum;
}
pre.code span.th {
  display: table-cell;
  user-select: none;
  -moz-user-select: none;
  -webkit-user-select: none;
  width: 2em;
  padding-right: 1.4em;
  color: rgb(153, 158, 169);
  font-family: "Consolas", "Arial";
}
pre.code span.th::before {
  content: counter(linenum);
  text-align: right;
  display: block;
}

#manageMenu {
  padding: 0 5px;
  width: 100%;
  height: 32px;
  box-sizing: border-box;
}

.manageBtn:hover {
  color: var(--ibtnHoverColor);
}

.manageBtn {
  width: 24px;
  height: 24px;
  text-align: center;
  font-size: 22px;
  color: var(--ibtnColor);
  cursor: pointer;
  padding: 4px;
  margin: 0 3px;
  -webkit-transition: all 0.15s ease-in-out;
  -moz-transition: all 0.15s ease-in-out;
  -ms-transition: all 0.15s ease-in-out;
  -o-transition: all 0.15s ease-in-out;
  transition: all 0.15s ease-in-out;
  float: left;
  display: flex;
}

#noteList {
  padding: 5px 0px;
  border: dotted rgb(210, 165, 168, 0) 2px;
  margin-top: 2px;
  border-radius: 20px;
  max-height: calc(100% - 80px);
  overflow-y: auto;
  width: calc(100% - 4px);
}

#noteList::-webkit-scrollbar {
  width: 12px;
}

#noteList::-webkit-scrollbar-thumb {
  border-radius: 6px;
  background: var(--scrollt1);
}

#noteList::-webkit-scrollbar-thumb:hover {
  background: var(--scrollt2);
}

#noteList::-webkit-scrollbar-thumb:active {
  background: var(--scrollt3);
}

#noteList::-webkit-scrollbar-track {
  border-radius: 6px;
  background: rgba(0, 10, 10, 0.08);
}

#signInBg {
  width: 100%;
  height: 100%;
  position: fixed;
  z-index: 99;
  left: 0px;
  top: 0px;
  background-color: rgba(0, 0, 0, 0.2);
  -webkit-backdrop-filter: blur(5px);
  backdrop-filter: blur(5px);
}

#signInWindow {
  width: 780px;
  height: 500px;
  position: absolute;
  left: calc((100% - 780px) / 2);
  top: calc((100% - 550px) / 2);
  border-radius: 25px;
  background-color: var(--blockBackC);
  box-shadow: 2px 2px 10px var(--windowShadowColor);
  padding: 5px 12px;
}

#SItitle {
  text-align: center;
  margin-top: 10px;
  margin-bottom: 10px;
}

.SIcloseBtn {
  position: absolute;
  right: 15px;
  top: 13px;
  width: 20px;
  height: 20px;
  background-size: 100%;
  font-size: 24px;
  line-height: 20px;
  cursor: pointer;
  color: var(--ibtnColor);
  -webkit-transition: all 0.15s ease-in-out;
  -moz-transition: all 0.15s ease-in-out;
  -ms-transition: all 0.15s ease-in-out;
  -o-transition: all 0.15s ease-in-out;
  transition: all 0.15s ease-in-out;
}

.SIcloseBtn:hover {
  color: var(--ibtnHoverColor);
}

#signBtn {
  height: 28px;
  width: 130px;
  margin: 7px 0% 12px calc(50% - 65px - 2px);
  line-height: 28px;
  text-align: center;
  border-radius: 16px;
  border: #4c4c4c 2px solid;
  border: var(--signBor);
  font-size: 14px;
  cursor: pointer;
  -webkit-transition: all 0.15s ease-in-out;
  -moz-transition: all 0.15s ease-in-out;
  -ms-transition: all 0.15s ease-in-out;
  -o-transition: all 0.15s ease-in-out;
  transition: all 0.15s ease-in-out;
}

#signBtn:hover {
  border: var(--signBor2);
}

.signBlock {
  width: 100%;
  height: 33px;
  display: flex;
  line-height: 33px;
}

.signHead {
  font-size: 20px;
  font-weight: bolder;
  flex: 1;
  text-align: center;
}

#signInStatus {
  flex: 1;
}

#signOutStatus {
  flex: 1;
}

#BgAnima {
  position: fixed;
  height: 100%;
  width: 100%;
  left: 0px;
  top: 0px;
  z-index: 0;
  display: block;
}

.cardMask {
  background-image: url("/images/card_mask.svg");
  background-color: rgb(14, 9, 21);
  cursor: pointer;
}

.cardValue {
  background: url("/images/card.png") center center no-repeat;
  text-indent: 29%;
  color: #ffffff;
}

#cardBox {
  margin: 1% 0px 0% 0px;
  padding:15px 60px;
  background: var(--divBack);
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between; /* 水平间距自适应 */
  gap: 15px; /* 控制卡片之间的间距，根据需要调整 */
}

.choose {
  box-shadow: 0px 0px 11px rgb(245, 225, 20);
}

.card {
  height: 50px;
  width: 136px;
  background-size: 100%;
  border-radius: 6px;
  display: inline-block;
  vertical-align: top;
  text-align: center;
  font-size: 22px;
  line-height: 50px;
}

#cardSubmit {
  color: var(--defaultColor);
  background-color: var(--btnBackC);
  border: var(--btnBor);
  width: 280px;
  margin: 2% 0px 3% calc(50% - 140px);
  height: 45px;
  border-radius: 12px;
  text-align: center;
  line-height: 45px;
  font-size: 18px;
  cursor: pointer;
  -webkit-transition: all 0.15s ease-in-out;
  -moz-transition: all 0.15s ease-in-out;
  -ms-transition: all 0.15s ease-in-out;
  -o-transition: all 0.15s ease-in-out;
  transition: all 0.15s ease-in-out;
}

#cardSubmit:hover {
  background-color: var(--btnBackC3);
  border: var(--btnBor2);
  color: var(--btnHoverColor) !important;
}

.turnback {
  animation-name: rotate;
  animation-duration: 4.4s;
  animation-delay: 0.4s;
}

@keyframes rotate {
  100% {
    transform: rotateY(2160deg);
  }
}

.hnItem {
  margin: 3px 1px 0px 1px;
  padding: 1px 2px 1px 0px;
  display: block;
  font-size: 15px;
  font-style: normal;
  cursor: pointer;
}

.hnTCon {
  display: inline-block;
  width: calc(100% - 29px);
}

.hnText {
  color: var(--hnColor1);
  /* overflow: hidden; */
  /* text-overflow: ellipsis; */
  /* white-space: nowrap; */
  display: inline-block;
  width: 100%;
}

.hnText:hover {
  color: var(--hnColor2);
  text-overflow: clip;
}

.hnIndex {
  width: 22px;
  margin-right: 7px;
  text-align: center;
  vertical-align: top;
  line-height: 24px;
  display: inline-block;
  color: var(--hnColor1);
}

.textEditing {
  background: var(--divEditBack);
  cursor: text;
}

#globalNewCard {
  display: block;
  margin-bottom: 25px;
  background: var(--rightblockBack);
  border-radius: 14px;
  opacity: 0.99;
  z-index: 10;
  overflow: hidden;
}

#hotNewCard {
  display: block;
  margin-bottom: 25px;
  background: var(--rightblockBack);
  border-radius: 14px;
  opacity: 0.99;
  z-index: 10;
  overflow: hidden;
}

.card-progress{
  background:var(--title-color);
  opacity:0.55;
  height: 3px;
  width: 0%;
  position: relative;
  top:-3px;
  -webkit-transition: width 2.4s linear;
  -moz-transition: width 2.4s linear;
  -ms-transition: width 2.4s linear;
  -o-transition: width 2.4s linear;
  transition: width 2.4s linear;
}

.card-header {
  margin: 0px 0% 0 0%;
  height: 36px;
  background: var(--headTitleBack);
  width: 100%;
  overflow: hidden;
}

#gn-data-wrapper {
  height: 280px;
  overflow: auto;
  scrollbar-width: none;
  -moz-scrollbars-style: none;
  margin: 0px 6px 3px 6px;
}

#hotNew-data-wrapper {
  height: 320px;
  overflow: auto;
  scrollbar-width: none;
  -moz-scrollbars-style: none;
  margin: 0px 6px 3px 6px;
}

#gn-data-wrapper::-webkit-scrollbar {
  width: 0 !important;
}

#hotNew-data-wrapper::-webkit-scrollbar {
  width: 0 !important;
}

#gn-title {
  font-size: 17px;
  line-height: 36px;
  margin-left: 30px;
  font-weight: bold;
  color: var(--title-color);
}

#hotNew-title {
  font-size: 17px;
  line-height: 36px;
  margin-left: 30px;
  font-weight: bold;
  color: var(--title-color);
}

#gn-refresh {
  height: 25px;
  margin: 3px 12px 0 0;
  padding: 4px;
  font-size: 22px;
  opacity: 0.3;
  display: flex;
  float: right;
  -webkit-transition: all 0.15s ease-in-out;
  -moz-transition: all 0.15s ease-in-out;
  -ms-transition: all 0.15s ease-in-out;
  -o-transition: all 0.15s ease-in-out;
  transition: all 0.15s ease-in-out;
  cursor: pointer;
}

#hotNew-refresh {
  height: 25px;
  margin: 3px 12px 0 0;
  padding: 4px;
  font-size: 22px;
  opacity: 0.3;
  display: flex;
  float: right;
  -webkit-transition: all 0.15s ease-in-out;
  -moz-transition: all 0.15s ease-in-out;
  -ms-transition: all 0.15s ease-in-out;
  -o-transition: all 0.15s ease-in-out;
  transition: all 0.15s ease-in-out;
  cursor: pointer;
}

#gn-refresh:hover {
  opacity: 1;
}

#hotNew-refresh:hover {
  opacity: 1;
}

.English-link {
  margin: 4px 0 9px 0;
}

.English-link-time {
  margin-right: 6px;
  color: var(--hnColor1);
  display: inline-block;
  width: 22px;
  vertical-align: top;
  font-size: 14px;
  line-height: 27px;
}

.English-info-box {
  display: inline-block;
  width: calc(100% - 40px);
}

.English-link-info {
  cursor: pointer;
  font-family: Proxima Nova, Helvetica, Arial, sans-serif;
  color: var(--hnColor1);
  font-size: 16px;
}

.English-link-info:hover {
  color: var(--hnColor2);
}

#themeCard {
  display: block;
  background: var(--rightblockBack);
  border-radius: 14px;
  opacity: 0.99;
  z-index: 10;
  margin-bottom: 25px;
  height: 42px;
  overflow: hidden;
  font-family: "Consolas";
}

#themeHead {
  height: 42px;
  line-height: 42px;
  width: 100%;
  overflow: hidden;
}

#themeTitle {
  display: inline-block;
  /* border-right: 1px solid black; */
  text-align: center;
  width: 70px;
  font-weight: bold;
  cursor: pointer;
  background: var(--headTitleBack);
  font-size: 20px;
  color: var(--title-color);
}

#themeName {
  display: inline-block;
  font-size: 16px;
}

#themeBar {
  display: flex;
  float: right;
  margin-right: 10px;
  margin-top: 6px;
  font-size: 25px;
  padding: 3px 5px;
  cursor: pointer;
  opacity: 0.8;
  -webkit-transition: all 0.15s ease-in-out;
  -moz-transition: all 0.15s ease-in-out;
  -ms-transition: all 0.15s ease-in-out;
  -o-transition: all 0.15s ease-in-out;
  transition: all 0.15s ease-in-out;
  color: var(--ibtnColor);
}

#themeBar:hover {
  color: var(--ibtnHoverColor);
  opacity: 1;
}

#themeList {
  border-top: 1px solid var(--horizontal-divider-color);
  overflow-y: scroll;
  height: 0px;
  margin: 0px 2px 4px 2px;
  padding: 0px 4px;
}

#themeList::-webkit-scrollbar {
  width: 9px;
}

#themeList::-webkit-scrollbar-thumb {
  border-radius: 5px;
  background: var(--scrollt1);
}

#themeList::-webkit-scrollbar-thumb:hover {
  background: var(--scrollt2);
}

#themeList::-webkit-scrollbar-thumb:active {
  box-shadow: 0 0 5px var(--scrolltshadow);
  -webkit-box-shadow: inset 0 0 3px rgba(0, 0, 0, 0.47);
  background: var(--scrollt3);
}

#themeList::-webkit-scrollbar-track {
  box-shadow: inset 0 0 3px rgba(0, 0, 0, 0.2);
  -webkit-box-shadow: inset 0 0 3px rgba(0, 0, 0, 0.2);
  border-radius: 5px;
  background: rgba(0, 0, 0, 0.1);
}

.themeItem {
  height: 30px;
  padding: 3px 3px 3px 10px;
  cursor: pointer;
  line-height: 30px;
  font-size: 15px;
  -webkit-transition: height 0.15s ease-out, line-height 0.15s ease-out;
  -moz-transition: height 0.15s ease-out, line-height 0.15s ease-out;
  -ms-transition: height 0.15s ease-out, line-height 0.15s ease-out;
  -o-transition: height 0.15s ease-out, line-height 0.15s ease-out;
  transition: height 0.15s ease-out, line-height 0.15s ease-out;
}

.themeItem:hover {
  background-color: var(--dropMenuBackC2);
  color: var(--dropMenuHoverColor);
  height: 36px;
  line-height: 36px;
}

#dailyCard {
  display: block;
  min-height: 30%;
  border-radius: 14px;
  opacity: 0.99;
  background-color: var(--blockBackC);
  z-index: 10;
  overflow: hidden;
}

#weather-box {
  width: 100%;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  font-size: 15px;
}

.weather-bar{
  position: absolute;
  top:95px;
  height: 120px;
  line-height: 120px;
  width: 20px;
  z-index: 102;
  cursor: pointer;
  -webkit-transition: all 0.15s ease-in-out;
  -moz-transition: all 0.15s ease-in-out;
  -ms-transition: all 0.15s ease-in-out;
  -o-transition: all 0.15s ease-in-out;
  transition: all 0.15s ease-in-out;
  color:var(--ibtnColor);
  opacity: 0.5;
  font-size: 28px;
}

.weather-bar:hover {
  color: var(--ibtnHoverColor);
  opacity: 1;
}

#weather-icon {
  width: calc(52px + 25%);
  opacity: 0.8;
  height: 115px;
  position: relative;
  overflow: hidden;
  left: 5px;
  color: var(--themeColor); /*帮助5天温度曲线图获得主题色*/
}

#weather-icon svg {
  fill: var(--themeColor);
  height: 130px;
  position: absolute;
  top: -10px;
  left: calc(40% - 60px);
}

#weather-panel {
  margin-top: 15px;
  width: calc(75% - 52px);
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  height: 100px;
}

#weather-city {
  height: 30px;
  line-height: 30px;
  font-size: 16px;
  cursor: pointer;
}

#weather-city:hover {
  font-weight: bold;
  text-decoration: underline;
}

#weatherInfoNow {
  height: 50px;
  line-height: 50px;
  width: 100%;
  text-align: center;
  margin-right: 15px;
}

#temperatureNow {
  font-size: 38px;
  font-weight: bold;
}

#weatherNow {
  font-size: 24px;
  margin-left: 5px;
}

#weatherFeel {
  margin: 2px 10px 2px 8px;
}

#weatherUV {
  margin: 2px 0 2px 8px;
}

#weatherDay {
  margin: 1px 0 2px 8px;
}

#pm25 {
  height: 25px;
  line-height: 25px;
  margin: 3px 2px 4px 8px;
  color: white;
  border-radius: 10px;
  padding: 0 4px;
  text-align: center;
  font-size: 13px;
}

.pm25_bg1 {
  background-color: rgba(50, 153, 90, 0.9);
}
.pm25_bg2 {
  background-color: rgba(104, 161, 24, 0.9);
}
.pm25_bg3 {
  background-color: rgba(211, 147, 18, 0.9);
}
.pm25_bg4 {
  background-color: rgba(194, 82, 7, 0.9);
}
.pm25_bg5 {
  background-color: rgba(168, 25, 25, 0.9);
}
.pm25_bg6 {
  background-color: rgba(106, 22, 48, 0.9);
}

.tIColor {
  display: inline-block;
  width: 11px;
  margin-left: 14px;
  z-index: 13;
  height: 11px;
  border: 1px solid var(--defaultColor);
}

.tIName {
  width: 60%;
  display: inline-block;
}

#weather-futurebox{
  width: 98%;
  position: relative;
  margin: 0 1%;
  height: 275px;
}

#weather-future {
  width: 200%;
  height: 273px;
  position: absolute;
}

.weather-future-item {
  position: relative;
  display: inline-block;
  height: 263px;
  width: 10%;
  font-size: 14px;
  padding: 5px 0;
}

.weather-future-week {
  height: 20px;
  line-height: 20px;
  width: 100%;
  text-align: center;
  display: inline-block;
}

.weather-future-date {
  height: 18px;
  line-height: 18px;
  width: 100%;
  text-align: center;
  display: inline-block;
  font-size: 13px;
}

.weather-future-day {
  height: 50px;
  line-height: 70px;
  display: block;
  margin-top: 5px;
  margin-bottom: 120px;
  text-align: center;
  position: relative;
  text-indent: 0.2em;
}

.weather-future-day svg {
  width: 100%;
  height: 36px;
  top: -4px;
  left: 0px;
  position: absolute;
  fill: var(--defaultColor);
}

.weather-future-night {
  height: 50px;
  line-height: 20px;
  display: block;
  text-align: center;
  position: relative;
  text-indent: 0.2em;
}

.weather-future-night svg {
  width: 100%;
  height: 36px;
  top: 14px;
  left: 0px;
  position: absolute;
  fill: var(--defaultColor);
}

#weather-chart {
  position: relative;
  width: 100%;
  height: 115px;
  top: -175px;
  left: 0px;
  z-index: 101;
}
