<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>网页夹管理</title>
  <link rel="shortcut icon" href="/images/favicon.ico">
  <link rel="stylesheet" type="text/css" href="/src/css/webfolder.css">
  <link rel="stylesheet" type="text/css" href="/src/css/websiteFrame.css">
  <link rel="stylesheet" type="text/css" href="/src/css/font awesome/all.min.css">
  <link rel="stylesheet" type="text/css" href="/node_modules/dragula/dist/dragula.min.css">
</head>

<body>
  <div id="headMenu">
    <nav id="headMenuNav">
      <ul id="headMenuLeft">
        <li><a href="/" class="thisWebsiteIcon"></a></li>
        <li class="headMenuElement" @mouseenter="openWebFolder()" @mouseleave="closeWebFolder()">
          <a href="webfolder" class="headMenuLink unChoose">网页夹 <i id="webMenuBar" class="fa-solid"
              :class="{'fa-caret-up':webBarIsClose,'fa-caret-down':!webBarIsClose}"></i></a>
          <div id="webFolderDropMenu"
            :style="{opacity:webFolderOpacity,visibility:webFolderVisibility,top:webFolderTop}">
            <ul id="webFolderList1">
              <li class="webFolderItem" @mouseover="webFolderHover(index)" v-for="(webFolderItem,index) in webFolder">
                {{webFolderItem[0].tag}}</li>
            </ul>
            <ul id="webFolderList2">
            </ul>
          </div>
        </li>
        <li class="headMenuElement"><a href="https://kcn74j8y3msl.feishu.cn/wiki/I2TswJ7hQiAbC9khDCtcXlUInrg"
            class="headMenuLink unChoose">个人中心</a></li>
        <li class="headMenuElement"><a href="https://kcn74j8y3msl.feishu.cn/wiki/BiZMwIToAiGhaJkOmp8cbtNfnie" class="headMenuLink unChoose">工作记录</a></li>
        <li class="headMenuElement"><a href="https://weread.qq.com/" class="headMenuLink unChoose">微信读书</a></li>
      </ul>
      <div id="searchCh" class="unChoose none">
        <span id="searchChText" :style="{borderRadius:searchOptionBorderRadius,boxShadow:searchOptionBoxShadow}"
          @mouseenter="openSearchOption()" @mouseleave="closeSearchOption()">{{this.searchCurWeb}}
          <div id="searchChDropMenu"
            :style="{opacity:searchOptionOpacity,visibility:searchOptionVisibility,top:searchOptionTop}">
            <ul>
              <li class="searchChOp" @click="this.searchCurWeb='百度'">百度</li>
              <li class="searchChOp" @click="this.searchCurWeb='谷歌'">谷歌</li>
              <li class="searchChOp" @click="this.searchCurWeb='谷歌学术'">谷歌学术</li>
              <li class="searchChOp" @click="this.searchCurWeb='秘塔'">秘塔</li>
              <li class="searchChOp" @click="this.searchCurWeb='豆包'">豆包</li>
              <li class="searchChOp" @click="this.searchCurWeb='必应'">必应</li>
              <li class="searchChOp" @click="this.searchCurWeb='Github'">Github</li>
            </ul>
          </div>
        </span>
      </div>
      <div class="headMenuSearchForm none" @keydown="headInputKeyCheck"
        :style="{borderRadius:searchBorderRadius,boxShadow:searchBoxShadow}" @mouseenter="openSearchForm()"
        @mouseleave="closeSearchForm()">
        <input id="headMenuSearchInput" placeholder="立即搜索" type="text" maxlength="192" v-model="searchValue">
        <div id="searchWB" class="fa-solid fa-magnifying-glass" @click="searchWebsite()"></div>
        <div id="searchDropMenu" :style="{opacity:searchOpacity,visibility:searchVisibility,top:searchTop}"
          @mouseup.stop="">
          <ul id="searchSuggestList">
            <li v-for="suggestion in this.suggestFolderList"
              @click="this.searchValue ='[网页] '+ suggestion.text;this.searchWebsite();">
              <img class="searchWebIcon" :src=suggestion.d></img><em>{{suggestion.a}}<strong
                  class="searchMatchWord">{{suggestion.b}}</strong>{{suggestion.c}}</em>
            </li>
            <li v-for="suggestion in this.suggestHistoryList"
              @click="this.searchValue =suggestion.text;this.searchWebsite();">
              <strong>[历史] </strong><em>{{suggestion.a}}<strong
                  class="searchMatchWord">{{suggestion.b}}</strong>{{suggestion.c}}</em>
            </li>
            <li v-for="suggestion in this.suggestWebList" @click="this.searchValue = suggestion;this.searchWebsite();">
              {{suggestion}}</li>
          </ul>
          <span class="searchHistory"
            v-if="this.suggestFolderList.length+this.suggestHistoryList.length+this.suggestWebList==0">
            <button v-for="(history,index) in searchHistoryList" class='searchHistoryItem' draggable="true"
              @dragstart="dragItem" type='button' @click='clickSearchHis(history)' :title=history
              @mouseover="this.showHistoryIconList[index]=true" @mouseout="this.showHistoryIconList[index]=false">
              <div class='searchHistoryText'>{{history}}</div>
              <i class='closeIcon fa-solid fa-circle-xmark' :class="{'none':!this.showHistoryIconList[index]}"
                @click.stop="" @mouseup.stop="deleteHistory(index)"></i>
            </button>
            <div class='noHistoryHint' :class="{'none':this.hasHistory}">暂无历史记录</div>
          </span>
          <div id="searchBottomBox">
            <button id="clearHistoryBtn" type="button" @click="clearHistory()"
              v-if="this.suggestFolderList.length+this.suggestHistoryList.length+this.suggestWebList==0">清空搜索历史</button>
            <button id="switchSuggestBtn" type="button"
              @click="switchSuggest()">{{suggestEnable?"关闭云端搜索联想":"启用云端搜索联想"}}</button>
          </div>
        </div>
      </div>
      <span class="unChoose" id="timeText" @mouseenter="enterTimeText()"
        @mouseleave="leaveTimeText()">{{timeText}}</span>
      <span id="calendarDropMenu" :style="{opacity:calendarOpacity,visibility:calendarVisibility,right:calendarPos}"
        @mouseleave="leaveCalendar()">
        <div id="calendarTimeText" :style="{padding:calendarTimePad,fontWeight:calendarTimeSize}">{{timeText}}</div>
        <table id="calendarTable" align="center" border="0" cellpadding="4" cellspacing="0" width="100%">
          <div id="calendarMonthBg" class="unChoose"></div>
          <thead>
            <tr>
              <th>一</th>
              <th>二</th>
              <th>三</th>
              <th>四</th>
              <th>五</th>
              <th>六</th>
              <th>日</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
            </tr>
            <tr>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
            </tr>
            <tr>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
            </tr>
            <tr>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
            </tr>
            <tr>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
            </tr>
            <tr>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
            </tr>
          </tbody>
        </table>
      </span>
    </nav>
    <div class="background"></div>
  </div>
  <div id="BgMask" class="hide"></div>
  <div id="backImage"></div>
  <div id="mainWindow">
    <div id="webFolderDis">
      <div id="webFolderTitle">
        <span>网页名</span>
        <span>组别</span>
        <span>网址</span>
        <span>描述</span>
        <span>图标</span>
        <span>操作项</span>
      </div>
      <div id="webFolderBody" onkeyup="return inputKeyCheck(event.keyCode,this)"></div>
    </div>
    <div style="display: flex; justify-content: space-evenly;">
      <div id="webFolderAdd" class="webFolderBtn" onclick="addOneWebsite()">新增</div>
      <div id="webFolderSubmit" class="webFolderBtn" onclick="submitWebFolder()">保存</div>
      <div id="webFolderReset" class="webFolderBtn" onclick="resetWebFolder()">重置</div>
    </div>

  </div>
  <div id="loadWin" class="hide unChoose">处理中<span id="loadBall"></span></div>
  <canvas id='BgAnima' class='hide'></canvas>
  <script src="/src/js/src/util.js"></script>
  <script src="/src/js/src/vue.global.js"></script>
  <script src="/node_modules/pinyin-pro/dist/index.js"></script>
  <script src="/src/js/PinyinCache.js"></script>
  <script src="/src/js/PinyinMatcher.js"></script>
  <script src="/node_modules/dragula/dist/dragula.min.js"></script>
  <script src="/src/js/websiteFrame.js"></script>
</body>

</html>
<script>

  function waitCartoon() {
    fade_in(document.getElementById("loadWin"))
    let cnt = 0
    document.getElementById('loadBall').style.animation = "animate 2s linear infinite"
    document.getElementById('loadWin').classList.add("loadWin1")
    fade_in(document.getElementById("BgAnima"))
    fade_in(document.getElementById("BgMask"))
  }


  function submitWebFolder() {
    //表格合法性审查
    if (document.getElementsByClassName("redText").length != 0) {
      alert("网页名含非法字符或关键内容为空!");
      return -1;
    }
    if (document.getElementsByClassName("redText2").length != 0) {
      alert("检测到网页名重复!");
      return -1;
    }

    //获取表格信息
    let newWebFolder = [];
    const tableList = document.getElementById("webFolderBody").children;
    for (let i = 0; i < tableList.length; i++) {
      let item = {};
      let web_name = tableList[i].children[0].children[0].value;
      let tag = tableList[i].children[1].children[0].value;
      let url = tableList[i].children[2].children[0].value;
      let description = tableList[i].children[3].children[0].value;
      let type = tableList[i].children[4].children[0].value;
      newWebFolder.push({ web_name: web_name, tag: tag, url: url, description: description, type: type });
    }
    let cntList = {};
    for (let i = 0; i < newWebFolder.length; i++) {
      if (cntList[newWebFolder[i].tag] == null) {
        cntList[newWebFolder[i].tag] = 1;
        newWebFolder[i].order = 1;
      }
      else {
        newWebFolder[i].order = ++cntList[newWebFolder[i].tag];
      }
    }
    //比对表格信息，统计变化
    let changeList = [], delList = [], addList = [], mdList = [];
    for (let i = 0; i < webFolder.length; i++) {
      let match = false;
      for (let j = 0; j < newWebFolder.length; j++) {
        //同一个名字，若有新内容则覆盖
        if (webFolder[i].web_name == newWebFolder[j].web_name) {
          if (webFolder[i].url != newWebFolder[j].url || webFolder[i].tag != newWebFolder[j].tag || webFolder[i].order != newWebFolder[j].order ||
            webFolder[i].description != newWebFolder[j].description || webFolder[i].type != newWebFolder[j].type) {
            mdList.push(newWebFolder[j]);
          }
          match = true;
          newWebFolder[j]["match"] = true;
          break;
        }
      }
      if (!match) {
        delList.push({ "web_name": webFolder[i].web_name, "type": webFolder[i].type });
      }
    }
    for (let i = 0; i < newWebFolder.length; i++) {
      if (newWebFolder[i]["match"] != true) {
        addList.push(newWebFolder[i]);
      }
      else {
        delete newWebFolder[i]["match"];
      }
    }
    changeList.push(delList);
    changeList.push(addList);
    changeList.push(mdList);
    if (addList.length == 0 && delList.length == 0 && mdList.length == 0) {
      alert("未检测到需要更新的内容");
      return;
    }
    console.info(JSON.stringify(changeList))
    //提交操作，反馈操作结果，并刷新页面
    let xmlhttp = new XMLHttpRequest();
    xmlhttp.open("POST", "http://127.0.0.1:6699/post/webfolder", true);
    xmlhttp.setRequestHeader("Content-type", "application/json");
    xmlhttp.send(JSON.stringify(changeList));
    xmlhttp.onload = () => {
      if (xmlhttp.readyState == 4 && xmlhttp.status == 200) {
        fade_out(document.getElementById("loadWin"), 800);
        fade_out(document.getElementById("BgAnima"), 1000, () => {
          document.getElementById('loadBall').style.animation = "";
          document.getElementById('loadWin').classList.remove("loadWin1");
          if (xmlhttp.responseText != "ok") {
            alert(xmlhttp.responseText);
          }
          else {
            alert("提交成功，确定后刷新页面");
          }
          location.reload();
        })
      }
    }
    waitCartoon();
  }
  let repearNameCheck = null;


  /**
   * 表格后验检查
   */
  function inputKeyCheck(code, tar) {
    const curE = document.activeElement;
    const curLine = curE.parentNode.parentNode;
    const tableList = document.getElementById("webFolderBody").children;
    if (curE.tagName !== 'INPUT') {
      return true;
    }
    let flag = true;
    const invalidChars = /[<>:"\/\\|?*\x00-\x1F]/g;
    if (invalidChars.test(curLine.children[0].children[0].value)) {
      curLine.children[0].classList.add("redText");
      curLine.children[1].classList.add("redText");
      curLine.children[2].classList.add("redText");
      flag = false;
    }
    for (let i = 0; i < 3; i++) {
      if (curLine.children[i].children[0].value.length == 0) {
        curLine.children[0].classList.add("redText");
        curLine.children[1].classList.add("redText");
        curLine.children[2].classList.add("redText");
        flag = false;
        break;
      }
    }
    if (flag && curLine.children[0].classList.contains("redText")) {
      curLine.children[0].classList.remove("redText");
      curLine.children[1].classList.remove("redText");
      curLine.children[2].classList.remove("redText");
    }
    if (repearNameCheck == null) {
      repearNameCheck = setTimeout(() => {
        for (let i = 0; i < tableList.length; i++) {
          tableList[i].children[0].classList.remove("redText2");
        }
        for (let i = 0; i < tableList.length; i++) {
          for (let j = i + 1; j < tableList.length; j++) {
            if (tableList[i].children[0].children[0].value != "" && tableList[i].children[0].children[0].value == tableList[j].children[0].children[0].value) {
              tableList[i].children[0].classList.add("redText2");
              tableList[j].children[0].classList.add("redText2");
            }
          }
        }
        repearNameCheck = null;
      }, 400);
    }

    return true;
  }

  function addOneWebsite() {
    const tb = document.getElementById("webFolderBody");
    let tag = `<div class="webFolderRow"><span class="redText"><input placeholder="不得为空" type="text" maxlength="24" ></span>\
      <span class="redText"><input placeholder="不得为空" type="text" maxlength="16" v></span>\
      <span class="redText"><input placeholder="不得为空" type="text" ></span>\
      <span><input type="text" maxlength="64" ></span>\
      <span><input type="text" maxlength="8" ></span>\
      <span><i class="far fa-times-circle opButton" onclick="deleteOneWebsite(this)" title="删除该网址链接"></i>\
        <i class="fa-solid fa-bars opButton" title="按住拖拽排序"></i></span></div>`;
    tb.insertAdjacentHTML("beforeend", tag);
    tb.scrollTop = tb.scrollHeight;
  }

  function deleteOneWebsite(target) {
    target.parentNode.parentNode.remove();
  }

  function resetWebFolder() {
    location.reload();
  }

  function initTable() {
    let xmlhttp = new XMLHttpRequest();
    xmlhttp.open("POST", "http://127.0.0.1:6699/post/getwebfolder", true);
    xmlhttp.send();
    xmlhttp.onload = () => {
      if (xmlhttp.readyState == 4 && xmlhttp.status == 200) {
        webFolder = JSON.parse(xmlhttp.responseText);
        const tb = document.getElementById("webFolderBody");
        for (let i = 0; i < webFolder.length; i++) {
          let tag = `<div class="webFolderRow"><span><input placeholder="不得为空" type="text" maxlength="24" value="${webFolder[i].web_name}"></span><span>\
            <input placeholder="不得为空" type="text" maxlength="16" value="${webFolder[i].tag}"></span>\
            <span><input placeholder="不得为空" type="text" value="${webFolder[i].url}"></span>\
            <span><input type="text" maxlength="64" value="${webFolder[i].description}"></span>\
            <span><input type="text" maxlength="8" value="${webFolder[i].type}"></span>\
            <span><i class="far fa-times-circle opButton" onclick="deleteOneWebsite(this)" title="删除该网址链接"></i>\
              <i class="fa-solid fa-bars opButton" title="按住拖拽排序"></i>\
              </span></div>`;
          tb.insertAdjacentHTML("beforeend", tag);
        }
      }
    }
  }

  function initDrake() {
    ToDoDrake = dragula([document.getElementById('webFolderBody')], {
      moves: function (el, container, handle) {
        return handle.classList.contains('fa-bars');
      }
    })
  }

  function main() {
    initTable();
    initDrake();
  }

  window.onload = main;
</script>