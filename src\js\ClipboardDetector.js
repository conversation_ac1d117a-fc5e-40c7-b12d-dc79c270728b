/**
 * 剪贴板检测器
 * 监听粘贴事件并检测剪贴板中的媒体内容，专门用于智能粘贴媒体功能
 */
class ClipboardDetector {
  /**
   * 构造函数
   * @param {HTMLElement} targetElement - 目标元素，通常是textarea
   */
  constructor(targetElement) {
    if (!targetElement) {
      throw new Error('ClipboardDetector: targetElement is required');
    }
    
    this.targetElement = targetElement;
    this.pasteHandler = null;
    this.isListening = false;
    
    // 支持的图片MIME类型
    this.supportedImageTypes = [
      'image/png',
      'image/jpeg', 
      'image/jpg',
      'image/gif',
      'image/webp'
    ];
    
    // 绑定this上下文
    this._handlePasteEvent = this._handlePasteEvent.bind(this);
  }

  /**
   * 附加粘贴事件监听器
   */
  attachPasteListener() {
    if (this.isListening) {
      console.warn('ClipboardDetector: Paste listener is already attached');
      return;
    }

    try {
      this.targetElement.addEventListener('paste', this._handlePasteEvent);
      this.isListening = true;
      console.log('ClipboardDetector: Paste listener attached successfully');
    } catch (error) {
      console.error('ClipboardDetector: Failed to attach paste listener:', error);
    }
  }

  /**
   * 分离粘贴事件监听器
   */
  detachPasteListener() {
    if (!this.isListening) {
      console.warn('ClipboardDetector: No paste listener to detach');
      return;
    }

    try {
      this.targetElement.removeEventListener('paste', this._handlePasteEvent);
      this.isListening = false;
      console.log('ClipboardDetector: Paste listener detached successfully');
    } catch (error) {
      console.error('ClipboardDetector: Failed to detach paste listener:', error);
    }
  }

  /**
   * 处理粘贴事件
   * @private
   * @param {ClipboardEvent} event - 粘贴事件对象
   */
  _handlePasteEvent(event) {
    try {
      // 检查剪贴板数据是否可用
      if (!event.clipboardData || !event.clipboardData.items) {
        console.log('ClipboardDetector: No clipboard data available');
        return; // 执行默认粘贴行为
      }

      const clipboardItems = event.clipboardData.items;
      const mediaInfo = this.detectMediaType(clipboardItems);

      if (mediaInfo) {
        // 检测到媒体内容，阻止默认粘贴行为
        event.preventDefault();
        console.log('ClipboardDetector: Media detected:', mediaInfo.type);
        
        // 触发自定义事件，传递媒体信息
        const customEvent = new CustomEvent('mediaDetected', {
          detail: {
            file: mediaInfo.file,
            mediaType: mediaInfo.type,
            targetElement: this.targetElement
          }
        });
        
        this.targetElement.dispatchEvent(customEvent);
      } else {
        // 没有检测到媒体内容，执行默认粘贴行为
        console.log('ClipboardDetector: No media detected, allowing default paste behavior');
      }
    } catch (error) {
      console.error('ClipboardDetector: Error handling paste event:', error);
      // 发生错误时不阻止默认行为，确保用户仍能正常粘贴文本
    }
  }

  /**
   * 检测剪贴板中的媒体类型
   * @param {DataTransferItemList} clipboardItems - 剪贴板项目列表
   * @returns {Object|null} 媒体信息对象，包含file和type属性，如果没有检测到媒体则返回null
   */
  detectMediaType(clipboardItems) {
    if (!clipboardItems || clipboardItems.length === 0) {
      return null;
    }

    try {
      // 遍历剪贴板项目
      for (let i = 0; i < clipboardItems.length; i++) {
        const item = clipboardItems[i];
        
        // 检查是否为文件类型
        if (item.kind === 'file') {
          const mimeType = item.type;
          
          // 检查是否为支持的图片类型
          if (this.supportedImageTypes.includes(mimeType)) {
            const file = item.getAsFile();
            
            if (file) {
              console.log(`ClipboardDetector: Detected image file - Type: ${mimeType}, Size: ${file.size} bytes`);
              
              return {
                file: file,
                type: mimeType
              };
            }
          } else if (mimeType.startsWith('image/')) {
            // 不支持的图片格式
            console.warn(`ClipboardDetector: Unsupported image format: ${mimeType}`);
            
            return {
              file: null,
              type: 'unsupported',
              originalType: mimeType
            };
          }
        }
      }
      
      return null; // 没有检测到支持的媒体类型
    } catch (error) {
      console.error('ClipboardDetector: Error detecting media type:', error);
      return null;
    }
  }

  /**
   * 检查是否正在监听
   * @returns {boolean} 是否正在监听粘贴事件
   */
  isListening() {
    return this.isListening;
  }

  /**
   * 获取支持的图片类型列表
   * @returns {string[]} 支持的MIME类型数组
   */
  getSupportedImageTypes() {
    return [...this.supportedImageTypes]; // 返回副本以防止外部修改
  }

  /**
   * 销毁检测器，清理资源
   */
  destroy() {
    this.detachPasteListener();
    this.targetElement = null;
    this.pasteHandler = null;
  }
}