:root[theme="FantasyDream-day"],
:root[theme="FantasyDream-night"] {
  --Ga0: #f6f7f8;
  --Ga1: #f1f2f3;
  --Ga2: #e3e5e7;
  --Ga3: #c9ccd0;
  --Ga4: #aeb3b9;
  --Ga5: #9499a0;
  --Ga6: #797f87;
  --Ga7: #61666d;
  --Ga8: #484c53;
  --Ga9: #2f3238;
  --Ga10: #18191c;
  --Ga11: #ffffff;
  --Ga12: #f1f2f3;
  --Wh0: #ffffff;
  --Ba0: #000000;
  --Pi0: #fff3f6;
  --Pi1: #ffecf1;
  --Pi2: #ffd9e4;
  --Pi3: #ffb3ca;
  --Pi4: #ff8cb0;
  --Pi5: #ff6699;
  --Pi6: #e84b85;
  --Pi7: #d03171;
  --Pi8: #ad1c5b;
  --Pi9: #771141;
  --Pi10: #3f0723;
  --Ma0: #fef3fc;
  --Ma1: #fdebfa;
  --Ma2: #fbd7f4;
  --Ma3: #f7aeeb;
  --Ma4: #f286e2;
  --Ma5: #ee5ddb;
  --Ma6: #da41cb;
  --Ma7: #c525ba;
  --Ma8: #9b1797;
  --Ma9: #670f67;
  --Ma10: #330834;
  --Re0: #fef3f2;
  --Re1: #feecea;
  --Re2: #fdd7d4;
  --Re3: #fcafaa;
  --Re4: #fa857f;
  --Re5: #f85a54;
  --Re6: #e23d3d;
  --Re7: #c9272c;
  --Re8: #9f1922;
  --Re9: #710e18;
  --Re10: #3b060d;
  --Or0: #fff6ee;
  --Or1: #fff0e3;
  --Or2: #ffe1c7;
  --Or3: #ffc18f;
  --Or4: #ffa058;
  --Or5: #ff7f24;
  --Or6: #e95b03;
  --Or7: #bb4100;
  --Or8: #8d2d00;
  --Or9: #5e1b00;
  --Or10: #2f0c00;
  --Ye0: #fffaef;
  --Ye1: #fff6e4;
  --Ye2: #ffeec9;
  --Ye3: #ffdb93;
  --Ye4: #ffc65d;
  --Ye5: #ffb027;
  --Ye6: #e58900;
  --Ye7: #b76800;
  --Ye8: #8a4a00;
  --Ye9: #5b2e00;
  --Ye10: #2f1600;
  --Ly0: #fffcec;
  --Ly1: #fffadf;
  --Ly2: #fff5bf;
  --Ly3: #ffea80;
  --Ly4: #ffdc40;
  --Ly5: #ffcc00;
  --Ly6: #d5a300;
  --Ly7: #aa7d00;
  --Ly8: #805a00;
  --Ly9: #553900;
  --Ly10: #2b1b00;
  --Lg0: #f7fbef;
  --Lg1: #f2f9e4;
  --Lg2: #e3f2c8;
  --Lg3: #c7e691;
  --Lg4: #a9d95b;
  --Lg5: #88cc24;
  --Lg6: #66b105;
  --Lg7: #4e8e04;
  --Lg8: #376a03;
  --Lg9: #224702;
  --Lg10: #102301;
  --Gr0: #effbf3;
  --Gr1: #e4f8ea;
  --Gr2: #caf1d6;
  --Gr3: #95e4af;
  --Gr4: #5fd689;
  --Gr5: #2ac864;
  --Gr6: #0eb350;
  --Gr7: #089043;
  --Gr8: #046e35;
  --Gr9: #034926;
  --Gr10: #012414;
  --Cy0: #edfbfb;
  --Cy1: #e2f8f8;
  --Cy2: #c4eff0;
  --Cy3: #89e1e1;
  --Cy4: #4fd3d1;
  --Cy5: #14c4bf;
  --Cy6: #02aaaa;
  --Cy7: #018488;
  --Cy8: #015f66;
  --Cy9: #013d44;
  --Cy10: #001d22;
  --Lb0: #ecfafe;
  --Lb1: #dff6fd;
  --Lb2: #bfedfa;
  --Lb3: #80daf6;
  --Lb4: #40c5f1;
  --Lb5: #00aeec;
  --Lb6: #008ac5;
  --Lb7: #00699d;
  --Lb8: #004b76;
  --Lb9: #002f4f;
  --Lb10: #001627;
  --Bl0: #f3f5ff;
  --Bl1: #ebefff;
  --Bl2: #d7dfff;
  --Bl3: #b0c1ff;
  --Bl4: #88a4ff;
  --Bl5: #6188ff;
  --Bl6: #4c6de4;
  --Bl7: #3752c8;
  --Bl8: #2136ac;
  --Bl9: #121f7f;
  --Bl10: #080d41;
  --Pu0: #f9f4ff;
  --Pu1: #f6edff;
  --Pu2: #eddbff;
  --Pu3: #d8b6ff;
  --Pu4: #c392ff;
  --Pu5: #ac6dff;
  --Pu6: #8f56e4;
  --Pu7: #723ecc;
  --Pu8: #5627b3;
  --Pu9: #371683;
  --Pu10: #190a44;
  --Br0: #faf8f6;
  --Br1: #f7f3f0;
  --Br2: #efe7e0;
  --Br3: #e0cfc1;
  --Br4: #d0b7a3;
  --Br5: #c19d84;
  --Br6: #a5816a;
  --Br7: #856553;
  --Br8: #634a3e;
  --Br9: #423029;
  --Br10: #211815;
  --Si0: #f9fbfc;
  --Si1: #f5f7fa;
  --Si2: #ebeff4;
  --Si3: #d7e0ea;
  --Si4: #c3d0df;
  --Si5: #afc0d5;
  --Si6: #8d9fb9;
  --Si7: #6d7f9c;
  --Si8: #4d5d7c;
  --Si9: #323d54;
  --Si10: #191e2b;
}

/* 我的空间 主题配色: FantasyDream-day*/
:root[theme="FantasyDream-day"] {
  --themeColor: #5d66e1;
  /*$主题色*/
  --themeColor2: rgb(212, 219, 249);

  --themeBack: #f3f3fa;

  --themeBack2: #f3f3faf0;

  /*主题背景色，内部参考调用*/


  /*配色接口*/

  color: #111111;
  /*默认字体颜色*/
  --defaultColor: #111111;
  /*默认字体颜色，提供给button、input等自带color属性的*/
  --title-color: #f0f0f0;
  --title-color2: var(--defaultColor);
  /*标题字体颜色*/
  --windowShadowColor: #11111170;
  /*弹窗、下拉框盒子阴影颜色*/

  --searchChColor: #f0f0f0;
  /*导航栏网址选择按钮字体颜色*/
  --back: linear-gradient(#dcdeee, #cfd0ee, #bfc4ee);
  /*$主页面背景*/
  --back2: linear-gradient(#dcdeee, #cfd0ee, #bfc4ee);
  /*$日志页面背景*/
  --back3: transparent;
  /*日志页面背景人物，可省略*/
  --bgSize1: cover;
  /*主页面背景缩放比*/
  --bgSize2: cover;
  /*日志页面背景缩放比*/
  --bgSize3: 57%;
  /*日志页面背景人物缩放比*/
  /* --theme: url("/images/maple.svg") center 230px no-repeat; */
  /*主题标志图*/
  --themeSize: 42%;
  /*主题标志缩放比*/
  --blockBackC: var(--themeBack);
  /*$主页窗体默认背景颜色*/
  --rightblockBack: linear-gradient(165deg,#f3f3fa 90px,#f3f3fa90 );
  /*$主页右侧窗体默认背景*/
  --blockBackC2: var(--themeBack);
  /*$日志页面窗体背景颜色*/

  --headMenuBack: transparent;
  /*导航栏背景*/
  --headMenuBack2: rgb(234, 234, 237);
  /*$导航栏固定时背景*/
  --headMenuColor: var(--defaultColor);
  /*导航栏跳转字体颜色*/
  --headMenuColor2: var(--themeColor);
  /*导航栏跳转字体悬浮颜色*/

  --inputBack: var(--themeBack);
  /*导航栏搜索框背景*/
  --searchChBackC: #6e77f4;
  /*导航栏网址选择按钮背景*/
  --dropMenuBackC: var(--themeBack);
  /*$导航栏搜索框和网址选择按钮下拉框背景*/
  --dropMenuBackC2: #6e77f4;
  /*$网址选择鼠标悬浮背景*/
  --dropMenuHoverColor: var(--themeBack2);
  /*下拉框悬浮字体颜色*/
  --dropMenuBorder: transparent;
  /*下拉框边框*/
  --headInputColor: var(--defaultColor);
  /*搜索框字体颜色*/
  --headInputColor2: black;
  /*搜索框字体聚焦颜色*/
  --headInputBack: var(--divBack);
  /*搜索框字体聚焦背景*/
  --headTitleBack: linear-gradient(140deg, #6f73e8, #6379f4, #928fe1);
  /*标题栏渐变背景*/
  --btnBackC: var(--themeColor2);
  /*$常规按钮背景*/
  --btnBackC2: transparent;
  /*$清除历史按钮背景*/
  --btnBackC3: #6e77f4;
  /*$常规按钮鼠标悬浮背景*/
  --btnHoverColor: var(--themeBack2);
  /*常规按钮悬浮字体颜色*/
  --btnHoverShadow: 0px 0px 3px var(--Ga6);
  /*常规按钮悬浮阴影*/

  /*--btnBor:2px solid rgb(145, 150, 145);/*常规按钮边框*/
  /*--btnBor2:2px solid var(--themeColor);/*常规按钮鼠标悬浮边框*/
  --ibtnColor: var(--Ga5);
  /*$图标按钮字体颜色*/
  --ibtnHoverColor: var(--themeColor);
  /*$图标按钮悬浮字体颜色*/
  --divBack: rgba(205, 205, 205, 0.5);
  /*笔记项目背景，抽卡页面背景*/
  --divEditBack: rgba(190, 190, 208, 0.5);
  /*笔记项目编辑时背景*/
  --noteBor: 2px solid transparent;
  /*笔记项目边框、签到状态框边框*/
  --noteBor2: 2px solid transparent;
  /*笔记项目鼠标悬浮边框、签到状态框鼠标悬浮边框*/
  --signBor: 2px solid var(--Ga5);
  /*签到状态框边框*/
  --signBor2: 2px solid var(--themeColor);
  /*签到状态框鼠标悬浮边框*/
  --footBack: linear-gradient(#bfc4ee, #aeb3d8);
  /*页尾背景*/
  --footColor: var(--defaultColor);
  /*页尾字体颜色*/
  --line: transparent;
  /*主页页尾上边框颜色*/
  --line2: transparent;
  /*搜索下拉框上边框颜色*/
  --line3: 1px solid rgb(197, 199, 237);
  /*导航栏固定时下边框颜色*/
  --horizontal-divider-color: rgb(194, 196, 237);
  /*note中hr标签分割线颜色*/
  --table-border-color: 0.1em solid #aaa;
  /*note中表格边界*/
  --hnColor1: #313131;
  /*热搜字体颜色*/
  --hnColor2: var(--themeColor);
  /*热搜鼠标悬浮字体颜色*/

  --seaItemBack: var(--themeColor2);
  /*$搜索历史项目背景*/
  --seaItemBack2: #6e77f4;
  /*搜索历史项目鼠标悬浮背景*/
  /*--seaItemBor:1px solid var(--Ga6);/*搜索历史项目边框*/
  /*--seaItemBor2:1px solid var(--Lb5);/*搜索历史项目鼠标悬浮边框*/
  --seaItemShadow: transparent; /*搜索历史项目阴影*/
  --seaItemColor: var(--defaultColor);
  /*搜索历史项目字体颜色*/
  --seaItemColor2: #f0f0f0;
  /*搜索历史项目鼠标悬浮字体颜色*/
  --seaItemIconColor: var(--themeColor2);
  /*搜索历史项目角标符号颜色*/

  /* --bodyScrollBack: linear-gradient(180deg, rgb(101, 93, 88) 49px, rgb(155, 145, 134)52px, rgb(157, 145, 131) 55%, rgb(166, 205, 78)85%); */
  /*页面所有滚动条槽背景*/
  --scrollt1: rgba(100, 100, 100, 0.7);
  /*页面所有滚动条颜色*/
  --scrollt2: rgba(70, 70, 70, 0.7);
  /*页面所有滚动条鼠标悬浮颜色*/
  --scrollt3: rgba(50, 50, 50, 0.8);
  /*页面所有滚动条鼠标按压颜色*/
  /*--scrolltshadow: rgba(230, 230, 230, 1);/*页面所有滚动条鼠标按压内部阴影颜色*/
}

/* 我的空间 主题配色: FantasyDream-night*/
:root[theme="FantasyDream-night"] {
  --themeColor: hsl(233, 78%, 84%);
  /*$主题色*/
  --themeColor2: hsl(233, 12%, 45%);

  --themeBack: hsl(233, 1%, 8%);

  /*主题背景色，内部参考调用*/


  /*配色接口*/

  color: hsl(233, 5%, 84%);
  /*默认字体颜色*/
  --defaultColor: hsl(233, 5%, 84%);
  /*默认字体颜色，提供给button、input等自带color属性的*/

  --defaultColor2: hsl(0, 0%, 88%);
  /*亮底字体颜色，提供给背景为主题色的文字*/

  --title-color: var(--defaultColor2);
  --title-color2: var(--defaultColor);
  /*标题字体颜色*/
  --windowShadowColor: rgba(97, 96, 117, 0);
  /*弹窗、下拉框盒子阴影颜色*/

  --searchChColor: var(--defaultColor2);
  /*导航栏网址选择按钮字体颜色*/

  --back: linear-gradient(
    hsl(233, 9%, 40%),
    hsl(235, 14%, 30%),
    hsl(240, 22%, 20%)
  );
  /*$主页面背景*/
  --back2: linear-gradient(
    hsl(233, 9%, 40%),
    hsl(235, 14%, 30%),
    hsl(240, 22%, 20%)
  );
  /*$日志页面背景*/
  --back3: transparent;
  /*日志页面背景人物，可省略*/
  --bgSize1: cover;
  /*主页面背景缩放比*/
  --bgSize2: cover;
  /*日志页面背景缩放比*/
  --bgSize3: 57%;
  /*日志页面背景人物缩放比*/
  /* --theme: url("/images/maple.svg") center 230px no-repeat; */
  /*主题标志图*/
  --themeSize: 42%;
  /*主题标志缩放比*/
  --blockBackC: var(--themeBack);
  /*$主页窗体默认背景颜色*/
  --rightblockBack: var(--themeBack);
  /*$主页右侧窗体默认背景*/
  --blockBackC2: var(--themeBack);
  /*$日志页面窗体背景颜色*/

  --headMenuBack: transparent;
  /*导航栏背景*/
  --headMenuBack2: hsl(233, 12%, 38%);
  /*$导航栏固定时背景*/
  --headMenuColor: var(--defaultColor2);
  /*导航栏跳转字体颜色*/
  --headMenuColor2: var(--themeColor);
  /*导航栏跳转字体悬浮颜色*/

  --inputBack: var(--themeBack);
  /*导航栏搜索框背景*/
  --searchChBackC: hsl(233, 67%, 62%);
  /*导航栏网址选择按钮背景*/
  --dropMenuBackC: var(--themeBack);
  /*$导航栏搜索框和网址选择按钮下拉框背景*/
  --dropMenuBackC2: hsl(233, 67%, 62%);
  /*$网址选择鼠标悬浮背景*/
  --dropMenuHoverColor: var(--defaultColor2);
  /*下拉框悬浮字体颜色*/
  --dropMenuBorder: 1px solid hsl(235, 10%, 26%);
  /*下拉框边框*/
  --headInputColor: hsl(233, 15%, 90%);
  /*搜索框字体颜色*/
  --headInputColor2: hsl(233, 15%, 90%);
  /*搜索框字体聚焦颜色*/
  --headInputBack: var(--divBack);
  /*搜索框字体聚焦背景*/
  --headTitleBack: linear-gradient(
    140deg,
    hsl(240, 67%, 62%),
    hsl(231, 82%, 62%),
    hsl(242, 53%, 67%)
  );
  /*标题栏渐变背景*/
  --btnBackC: var(--themeColor2);
  /*$常规按钮背景*/
  --btnBackC2: transparent;
  /*$清除历史按钮背景*/
  --btnBackC3: hsl(240, 67%, 62%);
  /*$常规按钮鼠标悬浮背景*/
  --btnHoverColor: var(--defaultColor2);
  /*常规按钮悬浮字体颜色*/
  --btnHoverShadow: 0px 0px 3px var(--Ga6);
  /*常规按钮悬浮阴影*/

  /*--btnBor:2px solid rgb(145, 150, 145);/*常规按钮边框*/
  /*--btnBor2:2px solid var(--themeColor);/*常规按钮鼠标悬浮边框*/
  --ibtnColor: var(--Ga5);
  /*$图标按钮字体颜色*/
  --ibtnHoverColor: var(--themeColor);
  /*$图标按钮悬浮字体颜色*/
  --divBack: hsl(233, 4%, 16%);
  /*笔记项目背景，抽卡页面背景*/
  --divEditBack: hsl(240, 7%, 19%);
  /*笔记项目编辑时背景*/
  --noteBor: 2px solid transparent;
  /*笔记项目边框、签到状态框边框*/
  --noteBor2: 2px solid transparent;
  /*笔记项目鼠标悬浮边框、签到状态框鼠标悬浮边框*/
  --signBor: 2px solid var(--Ga5);
  /*签到状态框边框*/
  --signBor2: 2px solid var(--themeColor);
  /*签到状态框鼠标悬浮边框*/
  --footBack: linear-gradient(
    hsla(220, 20%, 15%, 0),
    hsla(230, 18%, 10%, 1) 90%
  );
  /*页尾背景*/
  --footColor: #bbbbbb;
  /*页尾字体颜色*/
  --line: transparent;
  /*主页页尾上边框颜色*/
  --line2: 1px solid var(--themeBack);
  /*搜索下拉框上边框颜色*/
  --line3: 1px solid hsl(233, 16%, 55%);
  /*导航栏固定时下边框颜色*/
  --horizontal-divider-color: hsl(233, 34%, 55%);
  /*note中hr标签分割线颜色*/
  --table-border-color: 0.1em solid hsl(0, 0%, 67%);
  /*note中表格边界*/
  --hnColor1: var(--defaultColor);
  /*热搜字体颜色*/
  --hnColor2: var(--themeColor);
  /*热搜鼠标悬浮字体颜色*/

  --seaItemBack: var(--themeColor2);
  /*$搜索历史项目背景*/
  --seaItemBack2: hsl(240, 86%, 69%);
  /*搜索历史项目鼠标悬浮背景*/
  /*--seaItemBor:1px solid var(--Ga6);/*搜索历史项目边框*/
  /*--seaItemBor2:1px solid var(--Lb5);/*搜索历史项目鼠标悬浮边框*/
  --seaItemShadow: transparent; /*搜索历史项目阴影*/
  --seaItemColor: var(--defaultColor);
  /*搜索历史项目字体颜色*/
  --seaItemColor2: var(--defaultColor2);
  /*搜索历史项目鼠标悬浮字体颜色*/
  --seaItemIconColor: var(--defaultColor2);
  /*搜索历史项目角标符号颜色*/

  /* --bodyScrollBack: linear-gradient(180deg, rgb(101, 93, 88) 49px, rgb(155, 145, 134)52px, rgb(157, 145, 131) 55%, rgb(166, 205, 78)85%); */
  /*页面所有滚动条槽背景*/
  --scrollt1: hsla(0, 0%, 39%, 0.7);
  /*页面所有滚动条颜色*/
  --scrollt2: hsla(0, 0%, 27%, 0.7);
  /*页面所有滚动条鼠标悬浮颜色*/
  --scrollt3: hsla(0, 0%, 20%, 0.8);
  /*页面所有滚动条鼠标按压颜色*/
  /*--scrolltshadow: rgba(230, 230, 230, 1);/*页面所有滚动条鼠标按压内部阴影颜色*/
}
