/**
 * 拼音转换引擎
 * 封装pinyin-pro库，提供统一的拼音转换接口，支持缓存优化和错误处理
 */
class PinyinConverter {
  /**
   * 获取文本的完整拼音（无声调）
   * @param {string} text - 要转换的中文文本
   * @returns {string} 完整拼音字符串，不含空格
   */
  static getFullPinyin(text) {
    if (!text || typeof text !== 'string') {
      return {'full':'', 'index': null};
    }

    // 尝试从缓存获取
    if (typeof pinyinCache !== 'undefined') {
      const cached = pinyinCache.get(text, 'full');
      const cache_index = pinyinCache.get(text, 'index');
      if (cached !== null && cached !== '') {
        return {'full':cached, 'index':cache_index};
      }
    }

    try {
      // 预处理文本：处理特殊字符和数字
      const processedText = this._preprocessText(text);
      const result = window.pinyinPro.pinyin(processedText, {
        toneType: 'none',
        multiple: false, // 默认使用最常用读音，可以通过配置启用多音字支持
        type: 'all'
      });

      let result_text = "";
      let character_index_list = [0];
      for (let i = 0; i < result.length; i++) {
        if (result[i]["pinyin"] != ""){
          result_text += result[i]['pinyin'];
          character_index_list.push(character_index_list[i] + result[i]['pinyin'].length);
        }
        else{
          result_text += result[i]['origin'];
          character_index_list.push(character_index_list[i] + result[i]['origin'].length);
        }
      }
      const character_index_string = character_index_list.join(" ");
      // 存入缓存
      if (typeof pinyinCache !== 'undefined') {
        pinyinCache.set(text, 'full', result_text);
        pinyinCache.set(text, 'index', character_index_string);
      }
      return {'full':result_text, 'index': character_index_string};
    } catch (error) {
      console.warn('PinyinConverter.getFullPinyin error:', error, 'for text:', text);
      return {'full':'', 'index':null};
    }
  }


  /**
   * 检测输入是否为拼音字符
   * @param {string} input - 要检测的输入字符串
   * @returns {boolean} 如果输入只包含拼音字符（a-z）则返回true
   */
  static isPinyin(input) {
    if (!input || typeof input !== 'string') {
      return false;
    }

    // 去除首尾空格后检查是否只包含英文字母
    const trimmedInput = input.trim();
    if (trimmedInput.length === 0) {
      return false;
    }

    // 使用正则表达式检查是否只包含a-z字母或空格或数字（不区分大小写）
    return /^[a-zA-Z 0-9]+$/.test(trimmedInput);
  }

  /**
   * 预处理文本：处理特殊字符、数字和繁体字
   * @private
   * @param {string} text - 原始文本
   * @returns {string} 处理后的文本
   */
  static _preprocessText(text) {
    if (!text) return '';

    try {
      let processedText = text;
      processedText = processedText.replace(/[^\u4e00-\u9fff\u3400-\u4dbf\uf900-\ufaff\u3040-\u309f\u30a0-\u30ff\ua000-\ua48f\ua490-\ua4cf\uac00-\ud7af\u0020-\u007e]/g, '');

      return processedText;
    } catch (error) {
      console.warn('PinyinConverter._preprocessText error:', error);
      return text; // 返回原始文本作为降级方案
    }
  }
}


/**
 * 拼音匹配器
 * 实现拼音与中文文本的各种匹配模式
 */
class PinyinMatcher {
  /**
   * 匹配结果数据结构
   * @typedef {Object} MatchResult
   * @property {'full'|'hybrid'} matchType - 匹配类型
   * @property {string} matchedText - 匹配到的文本
   * @property {Array<{start: number, end: number}>} highlightRanges - 高亮范围数组
   * @property {number} score - 匹配得分，用于排序（越高越好）
   */

  /**
   * 全拼匹配 - 支持完整拼音匹配，包含错误处理和特殊情况
   * @param {string} input - 用户输入的拼音
   * @param {string} targetText - 目标中文文本
   * @returns {MatchResult|null} 匹配结果，无匹配时返回null
   */
  static fullMatch(input, targetText) {
    if (!input || !targetText || typeof input !== 'string' || typeof targetText !== 'string') {
      return null;
    }

    const normalizedInput = input.toLowerCase().trim();
    if (normalizedInput.length === 0 || !PinyinConverter.isPinyin(normalizedInput)) {
      return null;
    }
    let matchResult = this._tryDirectMatch(normalizedInput, targetText, 'full');
    if (matchResult) {
      return matchResult;
    }
    return null;

  }


  /**
   * 尝试直接匹配（不包含错误处理）
   * @private
   * @param {string} input - 标准化的输入
   * @param {string} targetText - 目标文本
   * @param {string} matchType - 匹配类型 ('full')
   * @returns {MatchResult|null} 匹配结果
   */
  static _tryDirectMatch(input, targetText, matchType) {
    try {
      let targetPinyin = '';
      let index_list;
      if (matchType === 'full') {
        targetPinyin = PinyinConverter.getFullPinyin(targetText);
        index_list = targetPinyin['index'].split(' ');
        targetPinyin = targetPinyin['full'].toLowerCase();
      }

      if (!targetPinyin) {
        return null;
      }
      const matchIndex = targetPinyin.indexOf(input);
      if (matchIndex === -1) {
        return null;
      }
      let start_character_index = -1, end_character_index = -1;
      for (let i = 0; i < index_list.length; i++) {
        if (start_character_index == -1 && matchIndex < index_list[i]) {
          start_character_index = i - 1;
        }
        if (end_character_index == -1 && matchIndex + input.length <= index_list[i]) {
          end_character_index = i;
          break;
        }
      }
      // 计算匹配得分
      let score = 50;
      if (matchIndex === 0) {
        score += 20;
      }
      if (input === targetPinyin) {
        score += 15;
      }

      // 计算高亮范围
      return {
        matchType: matchType,
        matchedText: targetText,
        highlightRanges: [{start: start_character_index, end: end_character_index}],
        score: score
      };
    } catch (error) {
      console.warn('PinyinMatcher._tryDirectMatch error:', error);
      return null;
    }
  }

  /**
   * 错误恢复匹配
   * @private
   * @param {string} input - 标准化的输入
   * @param {string} targetText - 目标文本
   * @param {string} matchType - 匹配类型 ('full')
   * @returns {MatchResult|null} 匹配结果
   */
  static _fallbackMatch(input, targetText, matchType) {
    try {
      // 简单的字符串包含匹配作为最后的降级方案
      const lowerTargetText = targetText.toLowerCase();
      if (lowerTargetText.includes(input)) {
        return {
          matchType: matchType,
          matchedText: targetText,
          highlightRanges: [{ start: 0, end: targetText.length }], // 高亮整个文本
          score: 10 // 很低的得分
        };
      }

      return null;
    } catch (error) {
      console.warn('PinyinMatcher._fallbackMatch error:', error);
      return null;
    }
  }

}