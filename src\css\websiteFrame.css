@font-face {
  font-family: 'JetBrains Mono';
  src:
    local('JetBrains Mono'),
    url('drake/JetBrainsMono-Regular.woff2') format('woff2');
  font-display: swap;
  font-weight: normal;
  font-style: normal;
}
@font-face {
  font-family: 'JetBrains Mono';
  src:
    local('JetBrains Mono'),
    url('drake/JetBrainsMono-Italic.woff2') format('woff2');
  font-display: swap;
  font-weight: normal;
  font-style: italic;
}
@font-face {
  font-family: 'JetBrains Mono';
  src:
    local('JetBrains Mono'),
    url('drake/JetBrainsMono-Bold.woff2') format('woff2');
  font-display: swap;
  font-weight: bold;
  font-style: normal;
}
@font-face {
  font-family: 'JetBrains Mono';
  src:
    local('JetBrains Mono'),
    url('drake/JetBrainsMono-BoldItalic.woff2') format('woff2');
  font-display: swap;
  font-weight: bold;
  font-style: italic;
}

@font-face {
  font-family: 'ZhuZiAWan';
  src: url('ZhuZiAWan.ttf');
  font-display: swap;
}

:root {
  --monospace: 'JetBrains Mono', Consolas, HYZhengYuan; /*代码字体*/
  --text-font: ZhuZiAWan; /*正文字体*/
  --title-font: var(--monospace); /*标题字体*/
  --text-line-height: 1.6; /*正文行间距*/
  --p-spacing: 0.8rem; /*段间距*/
}

* {
  margin: 0px;
}

a {
  color: var(--themeColor);
  text-decoration: none;
}

a:hover {
  color: var(--themeColor);
  text-decoration: underline;
}

ul {
  padding-left: 0px;
}

input {
  outline: none;
}

li {
  list-style: none;
}

img {
  border: 0;
  vertical-align: top;
  border: none;
}

button:focus {
  outline: none;
}

button {
  outline: none;
  border: transparent;
}

/* webkit, opera, IE9 （谷歌浏览器） 文本选中不变色*/
.unChoose {
  user-select: none;
  -webkit-user-select: none;
}

textarea {
  resize: none;
  background-color: transparent;
  border: none;
  color: inherit;
}

textarea:hover {
  outline: transparent;
}

textarea:focus {
  outline: transparent;
}

.hide {
  visibility: hidden;
}

.none {
  display: none !important;
}

option {
  padding-top: 10px;
  padding-bottom: 5px;
}

/*=====================================================*/

.headMenuLink {
  color: var(--headMenuColor);
  text-decoration: none;
  font-size: 17px;
  cursor: pointer;
}

.headMenuLink:visited {
  color: var(--headMenuColor);
  text-decoration: none;
}

.headMenuLink:hover {
  color: var(--headMenuColor2);
  text-decoration: none;
}

#headMenu {
  z-index: 85;
  min-width: 550px;
  width: 100%;
  position: fixed;
}

#headMenu .background {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  z-index: -1;
  background: var(--headMenuBack);
}

#headMenu .background.sliceDown {
  border-bottom: var(--line3);
  background: var(--headMenuBack2);
}

.headMenuElement {
  width: 95px;
  height: 40px;
  text-align: center;
  line-height: 40px;
}

#headMenuLeft {
  display: flex;
  flex-direction: row;
  align-items: center;
  flex-wrap: nowrap;
  padding-left: 10px;
}

#headMenuNav {
  height: 50px;
  display: flex;
}

#searchWB {
  font-size: 28px;
  padding: 3px;
  background: transparent;
  color: var(--ibtnColor);
  width: 28px;
  height: 28px;
  position: absolute;
  right: 1.2%;
  top: 5px;
  cursor: pointer;
  opacity: 0.6;
  -webkit-transition: all 0.15s ease-in-out;
  -moz-transition: all 0.15s ease-in-out;
  -ms-transition: all 0.15s ease-in-out;
  -o-transition: all 0.15s ease-in-out;
  transition: all 0.15s ease-in-out;
}
#searchWB:hover {
  color: var(--ibtnHoverColor);
  opacity: 0.9;
}

#headMenuSearchInput {
  -webkit-transition: all 0.15s ease-in-out;
  -moz-transition: all 0.15s ease-in-out;
  -ms-transition: all 0.15s ease-in-out;
  -o-transition: all 0.15s ease-in-out;
  transition: all 0.15s ease-in-out;
  border: none;
  outline: none;
  background: none;
  width: calc(98.8% - 54px);
  font-size: inherit;
  height: 34px;
  color: var(--headInputColor);
  border-radius: 6px;
  padding: 0 5px;
  margin: 0 6px;
}

#headMenuSearchInput:hover {
  color: var(--headInputColor2);
  background: var(--headInputBack);
}
#headMenuSearchInput:focus {
  color: var(--headInputColor2);
  background: var(--headInputBack);
}

.headMenuSearchForm {
  position: absolute;
  left: calc(45px + 36%);
  width: 28%;
  top: 5px;
  min-width: 250px;
  background: var(--inputBack);
  font-size: 16px;
  line-height: 44px;
  height: 44px;
  border-radius: 0px 16px 16px 0px;
}

#calendarTimeText {
  left: calc(105px + 30% + 300px + 8px);
  text-align: right;
  height: 34px;
  font-size: 17px;
  line-height: 34px;
  font-family: '微软雅黑', 'Times New Roman', serif;
  cursor: default;
  -webkit-transition:
    padding 0.35s ease-out,
    font-size 0.2s ease-in-out;
  -moz-transition:
    padding 0.35s ease-out,
    font-size 0.2s ease-in-out;
  -ms-transition:
    padding 0.35s ease-out,
    font-size 0.2s ease-in-out;
  -o-transition:
    padding 0.35s ease-out,
    font-size 0.2s ease-in-out;
  transition:
    padding 0.35s ease-out,
    font-size 0.2s ease-in-out;
}

#timeText {
  line-height: 50px;
  padding: 0 15px 0 0px;
  font-size: 17px;
  text-align: right;
  color: var(--headMenuColor);
  font-family: '微软雅黑', 'Times New Roman', serif;
  cursor: default;
  position: absolute;
  right: 0;
}

#searchCh {
  position: absolute;
  left: calc(36% - 45px);
  top: 5px;
  width: 90px;
  height: 44px;
  text-align: center;
}

#searchChDropMenu {
  position: absolute;
  left: 0px;
  padding: 4px 4px 6px 4px;
  border-radius: 0 0 12px 12px;
  width: 100%;
  border-top: var(--line2);
  box-sizing: border-box;
  background-color: var(--dropMenuBackC);
  border-bottom: var(--dropMenuBorder);
  border-left: var(--dropMenuBorder);
  border-right: var(--dropMenuBorder);
  opacity: 0;
  visibility: hidden;
  z-index: 40;
  font-weight: normal;
  color: var(--defaultColor);
  box-shadow: 1px 4px 4px var(--windowShadowColor);
  -webkit-transition: top 0.1s ease-in-out;
  -moz-transition: top 0.1s ease-in-out;
  -ms-transition: top 0.1s ease-in-out;
  -o-transition: top 0.1s ease-in-out;
  transition: top 0.1s ease-in-out;
}

#searchChText {
  font-weight: 600;
  background-color: var(--searchChBackC);
  position: absolute;
  left: 0px;
  top: 0px;
  width: 100%;
  height: 100%;
  line-height: 44px;
  color: var(--searchChColor);
  cursor: pointer;
  border-radius: 16px 0px 0px 16px;
}

#searchChText:hover {
  background-color: var(--dropMenuBackC2);
}

.searchChOp {
  font-size: 15px;
  height: 28px;
  line-height: 28px;
  -webkit-transition:
    height 0.15s ease-out,
    line-height 0.15s ease-out;
  -moz-transition:
    height 0.15s ease-out,
    line-height 0.15s ease-out;
  -ms-transition:
    height 0.15s ease-out,
    line-height 0.15s ease-out;
  -o-transition:
    height 0.15s ease-out,
    line-height 0.15s ease-out;
  transition:
    height 0.15s ease-out,
    line-height 0.15s ease-out;
}

.searchChOp:hover {
  background-color: var(--dropMenuBackC2);
  color: var(--dropMenuHoverColor);
  height: 34px;
  line-height: 34px;
}

#footContainer {
  height: 20px;
  border-top: var(--line);
  background: var(--footBack);
  color: var(--footColor);
  font-size: 16px;
  padding-top: 20px;
  padding-bottom: 20px;
  position: absolute;
  min-width: 1080px;
  width: 100%;
  z-index: 20;
  font-family: Consolas, 'Times New Roman', Times, Arial;
  text-align: center;
}

a.thisWebsiteIcon {
  background: url('/images/f256.svg') no-repeat center center;
  margin-top: 4px;
  margin-right: 5px;
  background-size: 100%;
  display: inline-block;
  width: 40px;
  height: 40px;
}

#searchDropMenu {
  position: absolute;
  left: 0px;
  padding: 1px 3px 8px 3px;
  border-radius: 0 0 10px 10px;
  width: 100%;
  box-sizing: border-box;
  border-top: var(--line2);
  border-bottom: var(--dropMenuBorder);
  border-right: var(--dropMenuBorder);
  border-left: var(--dropMenuBorder);
  background-color: var(--dropMenuBackC);
  opacity: 0;
  visibility: hidden;
  z-index: 40;
  box-shadow: 1px 4px 4px var(--windowShadowColor);
  -webkit-transition: top 0.1s ease-in-out;
  -moz-transition: top 0.1s ease-in-out;
  -ms-transition: top 0.1s ease-in-out;
  -o-transition: top 0.1s ease-in-out;
  transition: top 0.1s ease-in-out;
}

.searchHistory {
  display: block;
  line-height: 24px;
}

#searchSuggestList {
  margin: 0 0 2px 0;
  cursor: pointer;
}
#searchSuggestList li {
  height: 32px;
  line-height: 32px;
  padding: 0 8px;
  font-size: 15px;
  font-family: 'Microsoft YaHei', Arial, Helvetica, sans-serif;
  -webkit-transition:
    height 0.15s ease-out,
    line-height 0.15s ease-out;
  -moz-transition:
    height 0.15s ease-out,
    line-line-height 0.15s ease-out;
  -ms-transition:
    height 0.15s ease-out,
    line-height 0.15s ease-out;
  -o-transition:
    height 0.15s ease-out,
    line-height 0.15s ease-out;
  transition:
    height 0.15s ease-out,
    line-height 0.15s ease-out;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

.searchWebIcon {
  height: 20px;
  width: 20px;
  margin: 6px 10px 6px 6px;
  -webkit-transition: all 0.15s ease-out;
  -moz-transition: all 0.15s ease-out;
  -ms-transition: all 0.15s ease-out;
  -o-transition: all 0.15s ease-out;
  transition: all 0.15s ease-out;
}

#searchSuggestList li:hover .searchWebIcon {
  height: 24px;
  width: 24px;
  margin: 7px 10px 7px 4px;
}

.searchMatchWord {
  color: var(--themeColor);
}

#searchSuggestList li:hover {
  background-color: var(--dropMenuBackC2);
  color: var(--dropMenuHoverColor);
  height: 38px;
  line-height: 38px;
}

#searchSuggestList li:hover .searchMatchWord {
  color: var(--dropMenuHoverColor);
}

.sg-se {
  background-color: var(--dropMenuBackC2);
  color: var(--dropMenuHoverColor);
}

.sg-se .searchMatchWord {
  color: var(--dropMenuHoverColor);
}

#searchBottomBox {
  position: relative;
  left: 0;
  top: 5px;
  display: block;
  font-size: 14px;
  height: 25px;
  line-height: 25px;
}

#clearHistoryBtn {
  display: flex;
  float: right;
  border-radius: 10px;
  border: var(--btnBor3);
  cursor: pointer;
  margin-left: 5px;
  background-color: var(--btnBackC2);
  color: gray;
}

#clearHistoryBtn:hover {
  border: var(--btnBor4);
  color: var(--defaultColor);
}

#clearHistoryBtn:focus {
  border: var(--btnBor4);
  color: var(--defaultColor);
}

#switchSuggestBtn {
  display: flex;
  float: right;
  border-radius: 10px;
  border: var(--btnBor3);
  margin-left: 5px;
  cursor: pointer;
  background-color: var(--btnBackC2);
  color: gray;
}

#switchSuggestBtn:hover {
  border: var(--btnBor4);
  color: var(--defaultColor);
}

#switchSuggestBtn:focus {
  border: var(--btnBor4);
  color: var(--defaultColor);
}

.searchHistoryItem {
  position: relative;
  margin: 4px;
  cursor: pointer;
  border-radius: 6px;
  border: var(--seaItemBor);
  overflow: visible;
  background-color: var(--seaItemBack);
  color: var(--seaItemColor);
  -webkit-transition: all 0.15s ease-in-out;
  -moz-transition: all 0.15s ease-in-out;
  -ms-transition: all 0.15s ease-in-out;
  -o-transition: all 0.15s ease-in-out;
  transition: all 0.15s ease-in-out;
}

.searchHistoryItem:hover {
  border: var(--seaItemBor2);
  color: var(--seaItemColor2);
  box-shadow: var(--seaItemShadow);
  background-color: var(--seaItemBack2);
}

.noHistoryHint {
  font-size: 14px;
}

.closeIcon {
  position: absolute;
  display: block;
  width: 14px;
  height: 14px;
  top: -5px;
  right: -5px;
  padding: 2px;
  opacity: 0.9;
  color: var(--seaItemIconColor);
}

.searchHistoryText {
  max-width: 160px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin: 3px 6px;
  height: 18px;
  line-height: 18px;
  font-size: 13px;
}

#calendarDropMenu {
  position: absolute;
  top: 8px;
  width: 320px;
  background-color: var(--dropMenuBackC);
  border: var(--dropMenuBorder);
  box-shadow: 1px 3px 4px var(--windowShadowColor);
  border-radius: 12px;
  z-index: 40;
  overflow: hidden;
  -webkit-transition: right 0.4s ease-out;
  -moz-transition: right 0.4s ease-out;
  -ms-transition: right 0.4s ease-out;
  -o-transition: right 0.4s ease-out;
  transition: right 0.4s ease-out;
}

.moneyBox {
  margin: 15px 0 20px 0;
  height: 38px;
  line-height: 38px;
}

.moneySpan {
  cursor: default;
  background: var(--divBack);
  width: 70px;
  text-align: right;
  display: inline-block;
  position: relative;
  margin: 0 0 0 65px;
  border-radius: 0 16px 16px 0;
  padding: 0 15px;
}

#hcMoneyIcon {
  width: 54px;
  height: 54px;
  background: url('/images/合成玉.png') center center no-repeat;
  background-size: 100%;
  position: absolute;
  top: -8px;
  left: -35px;
}

#sourceStoneIcon {
  width: 54px;
  height: 54px;
  background: url('/images/至纯源石.png') center center no-repeat;
  background-size: 100%;
  position: absolute;
  top: -8px;
  left: -35px;
}

.moneyBtn {
  display: inline-block;
  width: 80px;
  color: var(--defaultColor);
  background-color: var(--btnBackC);
  border: var(--btnBor);
  cursor: pointer;
  text-align: center;
  margin-left: 40px;
  border-radius: 10px;
  font-size: 14px;
  height: 34px;
  margin-top: 2px;
  line-height: 34px;
  -webkit-transition: all 0.15s ease-in-out;
  -moz-transition: all 0.15s ease-in-out;
  -ms-transition: all 0.15s ease-in-out;
  -o-transition: all 0.15s ease-in-out;
  transition: all 0.15s ease-in-out;
}

.moneyBtn:hover {
  background-color: var(--btnBackC3);
  border: var(--btnBor2);
  color: var(--btnHoverColor) !important;
}

#calendarTable {
  margin: 5px 10px;
  width: 300px;
}

#calendarMonthBg {
  position: absolute;
  width: 100%;
  height: 260px;
  opacity: 0.2;
  font-size: 150px;
  text-align: center;
  line-height: 260px;
  font-style: italic;
  font-family: 'comic sans ms';
  color: var(--themeColor);
  z-index: -1;
}

.calendarDay {
  text-align: center;
  height: 26px;
  font-family: 'Consolas';
}

.calendarToday {
  color: var(--themeColor);
  font-size: 18px;
  font-weight: bold;
  text-decoration: underline;
  padding: 0px;
}

.calendarOtherMonth {
  font-size: 15px;
  opacity: 0.5;
}

.calendarThisMonth {
  font-size: 17px;
}

#webFolderDropMenu {
  position: relative;
  display: flex;
  width: 370px;
  left: -24px;
  border-radius: 12px;
  background-color: var(--dropMenuBackC);
  z-index: 40;
  box-shadow: 1px 3px 4px var(--windowShadowColor);
  border: var(--dropMenuBorder);
  min-height: 280px;
  padding: 5px 0;
  -webkit-transition: top 0.2s ease-in-out;
  -moz-transition: top 0.2s ease-in-out;
  -ms-transition: top 0.2s ease-in-out;
  -o-transition: top 0.2s ease-in-out;
  transition: top 0.2s ease-in-out;
  overflow: hidden;
}

#webFolderList1 {
  flex: 3;
  border-right: 1px var(--horizontal-divider-color) solid;
  overflow-y: auto;
}

#webFolderList2 {
  flex: 5;
  max-height: 480px;
  overflow-y: auto;
}

#webFolderList1::-webkit-scrollbar {
  width: 4px;
}
#webFolderList1::-webkit-scrollbar-thumb {
  background: var(--scrollt1);
}
#webFolderList1::-webkit-scrollbar-thumb:hover {
  background: var(--scrollt2);
}

#webFolderList2::-webkit-scrollbar {
  width: 6px;
}
#webFolderList2::-webkit-scrollbar-thumb {
  background: var(--scrollt1);
}
#webFolderList2::-webkit-scrollbar-thumb:hover {
  background: var(--scrollt2);
}

.webFolderItem {
  height: 38px;
  line-height: 38px;
  align-items: center;
  cursor: default;
  font-size: 14px;
  -webkit-transition:
    height 0.3s ease-out,
    line-height 0.3s ease-out;
  -moz-transition:
    height 0.3s ease-out,
    line-height 0.3s ease-out;
  -ms-transition:
    height 0.3s ease-out,
    line-height 0.3s ease-out;
  -o-transition:
    height 0.3s ease-out,
    line-height 0.3s ease-out;
  transition:
    height 0.3s ease-out,
    line-height 0.3s ease-out;
}
.webFolderItemHover {
  background-color: var(--dropMenuBackC2) !important;
  color: var(--dropMenuHoverColor);
  height: 50px;
  line-height: 50px;
}

.webItem {
  height: 34px;
  line-height: 34px;
  font-size: 13px;
  cursor: pointer;
  text-align: left;
  -webkit-transition:
    height 0.15s ease-out,
    line-height 0.15s ease-out;
  -moz-transition:
    height 0.15s ease-out,
    line-height 0.15s ease-out;
  -ms-transition:
    height 0.15s ease-out,
    line-height 0.15s ease-out;
  -o-transition:
    height 0.15s ease-out,
    line-height 0.15s ease-out;
  transition:
    height 0.15s ease-out,
    line-height 0.15s ease-out;
  position: relative;
}

.webItem:hover {
  background-color: var(--dropMenuBackC2) !important;
  color: var(--dropMenuHoverColor);
  height: 40px;
  line-height: 40px;
}

.webItemIcon {
  height: 18px;
  width: 18px;
  margin: 8px 12px 8px 18px;
  -webkit-transition: all 0.15s ease-out;
  -moz-transition: all 0.15s ease-out;
  -ms-transition: all 0.15s ease-out;
  -o-transition: all 0.15s ease-out;
  transition: all 0.15s ease-out;
}

.webItem:hover .webItemIcon {
  height: 24px;
  width: 24px;
  margin: 8px 12px 8px 15px;
}

#webMenuBar {
  position: relative;
  top: 1.5px;
}
