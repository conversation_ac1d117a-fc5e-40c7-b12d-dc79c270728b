.mainBody {
  background: var(--back3);
  background-size: var(--bgSize3);
  width: 100%;
  height: 100vh;
  z-index: 5;
  min-width: 1000px;
  z-index: 10;
  position: fixed;
}

.mainWindow {
  z-index: 20;
  margin: 70px auto 50px auto;
  width: 93%;
  max-width: 1440px;
  height: calc(100% - 120px);
}

.mainBox{
  background-color: var(--blockBackC2);
  border-radius: 14px;
  padding: 0.3% 1.2% 0.8% 1.8%;
  opacity: 0.8;
  margin:0 20px;
  width: calc(97% - 40px);
  height: 100%;
  position: relative;
  /* box-shadow: 2px 2px 10px var(--windowShadowColor); */
}

.logText {
  height: calc(100% - 75px);
  display: block;
  overflow-y: scroll;
  padding-right: 5px;
  font-family: var(--text-font) !important;
  color: var(--text-color);
  -webkit-font-feature-settings: "liga" on, "calt" on;
  font-feature-settings: "liga" on, "calt" on;
  -webkit-font-smoothing: subpixel-antialiased;
  text-rendering: optimizeLegibility;
  overflow-x: hidden;
}
.logTitle {
  text-align: center;
  margin: 8px 0 10px 0;
  width: 100%;
  font-size: 26px;
  font-weight: bolder;
  display: inline-block;
}
.logSubTitle {
  font-size: 20px;
  font-family: "Consolas", "黑体";
  position: absolute;
  right: 16px;
  top: 24px;
}

.logText::-webkit-scrollbar {
  width: 14px;
}

.logText::-webkit-scrollbar-thumb {
  background: var(--scrollt1);
}
.logText::-webkit-scrollbar-thumb:hover {
  background: var(--scrollt2);
}
.logText::-webkit-scrollbar-thumb:active {
  background: var(--scrollt3);
}

.logText::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
}

#backImage {
  z-index: 0;
  position: fixed;
  top: 0;
  left: 0%;
  width: 100%;
  background: var(--back2);
  height: 100%;
  background-size: var(--bgSize2);
}

hr {
  height: 2px;
  padding: 0;
  margin: 0 0 8px 0;
  background: var(--horizontal-divider-color);
  border: 0 none;
  overflow: hidden;
  box-sizing: content-box;
}

.logText h1,
.logText h2,
.logText h3,
.logText h4,
.logText h5,
.logText h6 {
  font-family: var(--title-font);
  position: relative;
  margin-top: 1.8rem;
  margin-bottom: 1rem;
  font-weight: bold;
  line-height: 1.4;
  cursor: text;
  color: var(--title-color2);
}

.logText > h1 {
  font-size: 2rem;
  text-align: center;
  margin-top: 0;
}

.logText > h3 {
  font-size: 1.4rem;
}

.logText > h4 {
  font-size: 1.2rem;
}

.logText > h5 {
  font-size: 1rem;
}

.logText > h6 {
  font-size: 1rem;
}

.logText > h2:first-child {
  margin-top: 0;
  padding-top: 0;
}

.logText > h1:first-child {
  margin-top: 0;
  padding-top: 0;
}

.logText > h1:first-child + h2 {
  margin-top: 0;
  padding-top: 0;
}

.logText > h3:first-child,
.logText > h4:first-child,
.logText > h5:first-child,
.logText > h6:first-child {
  margin-top: 0;
  padding-top: 0;
}

.logText > *:first-child {
  margin-top: 0 !important;
}

.logText > *:last-child {
  margin-bottom: 0 !important;
}

.logText > li > ol,
.logText > li > ul {
  margin: 0 0;
}

.logText ul {
  list-style-type: disc;
}

.logText ul > li {
  display: list-item;
  list-style-type: disc;
  text-align: -webkit-match-parent;
  line-height: var(--text-line-height);
  margin: var(--p-spacing) 0;
}

.logText > img {
  border-left: none;
  border-right: none;
  vertical-align: baseline;
  border-radius: 2px;
}

/* .logText >p,
.logText >blockquote,
.logText >ul,
.logText >ol,
.logText >dl,
.logText >table {
    margin: var(--p-spacing) 0;
} */

.logText > ul,
ol {
  padding-inline-start: 2em;
}

.logText > ul:first-child,
.logText > ol:first-child {
  margin-top: 0;
}

.logText > ul:last-child,
.logText > ol:last-child {
  margin-bottom: 0;
}

.logText > a:first-child h1,
.logText > a:first-child h2,
.logText > a:first-child h3,
.logText > a:first-child h4,
.logText > a:first-child h5,
.logText > a:first-child h6 {
  margin-top: 0;
  padding-top: 0;
}
