const log4js = require('log4js');


log4js.configure({
    appenders:
    {
        console:
        {
            type: 'console',
        },
        datelog:
        {
            type: 'dateFile',
            filename: 'tmp/serve.log',
            pattern: ".yyyy-MM-dd.txt",
            numBackups: 10,
        },
    },
    categories:
        {
            default:{
                appenders: ['console'],
                level: 'debug',
            },
            datelog:
            {
                appenders: ['console', 'datelog'],
                level: 'debug', // 指定等级
            },
        },
});
    
    
function getLogger(type)
{
    return log4js.getLogger(type);
}

module.exports = {
    getLogger,
}

const log = getLogger('datelog');
log.info("Log is on")