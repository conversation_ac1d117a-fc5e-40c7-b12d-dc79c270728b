/*
nodejs v20.11.1  npm v10.2.4 nvm v1.1.11
稳定版使用pkg5.8.0打包为exe 具体使用node command找到对应的路径使用指令 如 pkg -t node16-win-x64 .\servers\server.js
*/
const VERSION = '2.6-a29-s9'; //大版本-开发分支版本-后端打包版本
const fs = require('fs');
const fsp = require('fs').promises;
const path = require('path');
const log4js = require('./logger');
const logger = log4js.getLogger('datelog');
const express = require('express');
const cheerio = require('cheerio');
const puppeteer = require('puppeteer');
const app = express();
const axios = require('axios');
const MersenneTwister = require('mersenne-twister');
const multer = require('multer');

class SystemSetting {
  root;
  port;
  browser_path;
  online;
  zhnew;
  engnew;
}

class UserPublicData {
  //用户公开数据，需要存储且可同步至前端
  HCmoney;
  signFlag;
  signInTime;
  signOutTime;
  sourceStone;
  signCardInfo;
  city;
}

class UserPrivateData {
  //用户后端数据，需要存储但无需同步至前端
  signCard;
}
class UserRunTimeData {
  //用户运行缓存数据，无需存储也无需同步至前端
  hotUpdateTime;
  weatherUpdateTime;
}

let userPublic = new UserPublicData();
let userPrivate = new UserPrivateData();
let userTemp = new UserRunTimeData();
let systemSetting = new SystemSetting();

//读取系统设置setting.json，如果该文件不存在，系统终止
function init_system() {
  let data = fs.readFileSync('./setting.json', 'utf-8');
  data = JSON.parse(data);
  systemSetting.root = data.root_path;
  systemSetting.port = data.port;
  systemSetting.browser_path = data.browser_path;
  systemSetting.online = data.online;
  systemSetting.zhnew = data.zhnew;
  systemSetting.engnew = data.engnew;
  userTemp.hotUpdateTime = 0;
}

init_system();
app.use(express.static(systemSetting.root));
app.use(express.urlencoded({ extended: false }));

// Configure multer for media upload
const storage = multer.memoryStorage();
const upload = multer({
  storage: storage,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
  },
  fileFilter: (req, file, cb) => {
    // Support PNG, JPEG, GIF, WebP formats
    const allowedTypes = ['image/png', 'image/jpeg', 'image/jpg', 'image/gif', 'image/webp'];
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('不支持的文件格式，仅支持PNG、JPEG、GIF、WebP格式'), false);
    }
  }
});

function shuffle(arr) {
  let result = [],
    random;
  let generator = new MersenneTwister((Math.random() * 9301 + 49297) % 233280);
  while (arr.length > 0) {
    random = Math.floor(generator.random() * arr.length);
    result.push(arr[random]);
    arr.splice(random, 1);
  }
  return result;
}

app.listen(systemSetting.port, () => {
  logger.info('express server run at 127.0.0.1:' + systemSetting.port);
  logger.info('server version:' + VERSION);
});

app.get('/', (req, res) => {
  read_data();
  fs.readFile(path.join(systemSetting.root, './src/Index.html'), 'utf-8', (err, dataStr) => {
    if (err) {
      res.status = 404;
      logger.error('Index.html 读取失败');
      return -1;
    }
    logger.info('get  /');
    res.status = 200;
    res.send(dataStr);
  });
});

app.get('/webfolder', (req, res) => {
  fs.readFile(path.join(systemSetting.root, './src/WebFolder.html'), 'utf-8', (err, dataStr) => {
    if (err) {
      res.status = 404;
      logger.error('WebFolder.html 读取失败');
      return -1;
    }
    res.status = 200;
    res.send(dataStr);
  });
});

app.get('/signCard', async (req, res) => {
  let message = req.url.split('?')[1];
  let curT = new Date();
  if ((systemSetting.online = true)) {
    await read_data();
  }
  if (
    userPrivate.signCard.split('-')[0] != curT.getFullYear() ||
    userPrivate.signCard.split('-')[1] != curT.getMonth() + 1 ||
    userPrivate.signCard.split('-')[2] != curT.getDate()
  ) {
    res.status = 200;
    userPrivate.signCard = curT.getFullYear() + '-' + curT.getMonth() + 1 + '-' + curT.getDate();
    let t = parseInt(userPublic.signInTime.split(':')[0]),
      m = parseInt(userPublic.signInTime.split(':')[1]);
    let card = [800, 600, 600, 400, 400, 300, 300, 300];
    let ans;
    let response;
    card = shuffle(card);

    //奖池一共含8张含有合成玉的幸运卡，包括1张【800】，2张【600】，2张【400】，3张【300】。
    //7-7点半：随机使5张签到卡的数额增加300合成玉，抽4张，获得数额最大的奖励；7点半-8点：抽4张；8点-8点半：抽2张；8点半-10点：抽1张。
    if (t == 7 && m < 30) {
      //随机使4张签到卡的数额增加300合成玉
      let idx = shuffle([0, 1, 2, 3, 4, 5, 6, 7]);
      for (let i = 0; i < 5; i++) {
        card[idx[i]] += 300;
      }
      ans = Math.max(
        card[parseInt(message[0])],
        card[parseInt(message[1])],
        card[parseInt(message[2])],
        card[parseInt(message[3])]
      );
      response =
        card[parseInt(message[0])] +
        ',' +
        card[parseInt(message[1])] +
        ',' +
        card[parseInt(message[2])] +
        ',' +
        card[parseInt(message[3])];
    } else if (t == 7) {
      ans = Math.max(
        card[parseInt(message[0])],
        card[parseInt(message[1])],
        card[parseInt(message[2])],
        card[parseInt(message[3])]
      );
      response =
        card[parseInt(message[0])] +
        ',' +
        card[parseInt(message[1])] +
        ',' +
        card[parseInt(message[2])] +
        ',' +
        card[parseInt(message[3])];
    } else if (t == 8 && m < 30) {
      ans = Math.max(card[parseInt(message[0])], card[parseInt(message[1])]);
      response = card[parseInt(message[0])] + ',' + card[parseInt(message[1])];
    } else if (t < 10) {
      ans = card[parseInt(message[0])];
      response = ans;
    } else {
      ans = 0;
      response = 0;
    }
    res.send(response.toString());
    logger.info('今日签到抽卡获得合成玉：' + ans);
    userPublic.HCmoney += ans;
    userPrivate.signCard = curT.getFullYear() + '-' + (curT.getMonth() + 1) + '-' + curT.getDate();
    userPublic.signCardInfo = response;
    for (let i = 0; i < message.length; i++) userPublic.signCardInfo += ',' + message[i];
    saveUserData();
  } else {
    res.status = 400;
    res.send('');
    logger.info('收到签到抽卡请求，但今日已抽卡');
  }
});

function saveUserData() {
  let str =
    userPublic.HCmoney +
    '\n' +
    userPublic.signFlag +
    '\n' +
    userPublic.signInTime +
    '\n' +
    userPublic.signOutTime +
    '\n' +
    userPublic.sourceStone +
    '\n' +
    userPrivate.signCard +
    '\n' +
    userPublic.signCardInfo +
    '\n' +
    userPublic.city;
  fs.writeFile(path.join(systemSetting.root, './data/user'), str, function (err) {
    if (err) {
      return logger.error('userData保存失败');
    }
    logger.log('userData保存成功');
  });
}

app.get('/log', (req, res) => {
  fs.readFile(path.join(systemSetting.root, './src/Log.html'), 'utf-8', (err, dataStr) => {
    if (err) {
      logger.error('Log.html 读取失败');
      return 0;
    }
    res.send(dataStr);
  });
});

async function getWeather() {
  const city = userPublic.city;
  const cityCode = encodeURIComponent(city + '天气');
  try {
    const { data } = await axios.get(`https://weathernew.pae.baidu.com/weathernew/pc?query=${cityCode}&srcid=4982`, {
      headers: {
        'User-Agent':
          'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
      },
    });
    let weather_msg = {};
    weather_msg.temperature = data.match(/"temperature":"(.*?)"/)[1];
    weather_msg.weather = JSON.parse(`"${data.match(/"weather":"(.*?)"/)[1]}"`);
    weather_msg.bodytemp = JSON.parse(`"${data.match(/"bodytemp_info":"(.*?)"/)[1]}"`);
    weather_msg.pm25 = data.match(/"ps_pm25":"(.*?)"/)[1];
    weather_msg.temperature_night = data.match(/"temperature_night":"(.*?)"/)[1];
    weather_msg.temperature_day = data.match(/"temperature_day":"(.*?)"/)[1];
    weather_msg.uv = JSON.parse(`"${data.match(/"uv":"(.*?)"/)[1]}"`);
    weather_msg.uv_info = JSON.parse(`"${data.match(/"uv_info":"(.*?)"/)[1]}"`);
    weather_msg.weather_day = JSON.parse(`"${data.match(/"weather_day":"(.*?)"/)[1]}"`);
    weather_msg.weather_night = JSON.parse(`"${data.match(/"weather_night":"(.*?)"/)[1]}"`);
    weather_msg.wind_direction_day = JSON.parse(`"${data.match(/"wind_direction_day":"(.*?)"/)[1]}"`);
    weather_msg.wind_power_day = JSON.parse(`"${data.match(/"wind_power_day":"(.*?)"/)[1]}"`);
    weather_msg.wind_direction_night = JSON.parse(`"${data.match(/"wind_direction_night":"(.*?)"/)[1]}"`);
    weather_msg.wind_power_night = JSON.parse(`"${data.match(/"wind_power_night":"(.*?)"/)[1]}"`);
    weather_msg.time = data.match(/"update_time":"(.*?)"/)[1];
    weather_msg.city = city;
    let days_list = [];
    let yesterday_msg = JSON.parse(data.match(/"yesterday_15d":({.*?}),"do/)[1]);
    days_list.push({
      day: yesterday_msg.date,
      temperature_day: yesterday_msg.temperature_day,
      temperature_night: yesterday_msg.temperature_night,
      weather_day: yesterday_msg.weather_day,
      weather_night: yesterday_msg.weather_night,
    });
    let days15_msg = JSON.parse(data.match(/"15_day_forecast":{"info":(\[.*?\]),/)[1]);
    for (let i = 0; i < 9; i++) {
      days_list.push({
        day: days15_msg[i].date,
        temperature_day: days15_msg[i].temperature_day,
        temperature_night: days15_msg[i].temperature_night,
        weather_day: days15_msg[i].weather_day,
        weather_night: days15_msg[i].weather_night,
      });
    }
    weather_msg.days_list = days_list;
    weather_msg.code = 200;
    fs.writeFile(
      path.join(systemSetting.root, './tmp/weather.json'),
      JSON.stringify(weather_msg),
      { encoding: 'utf8' },
      err => {
        if (err) {
          logger.error('weather.json写入失败');
        }
      }
    );
    userTemp.weatherUpdateTime = new Date().getTime();
    return weather_msg;
  } catch (error) {
    logger.warn(`网络异常，${city}天气数据拉取失败，异常如下：`, error);
    return { code: 500, city: city };
  }
}

/**
 * @description: 设置城市
 * @param {string} city
 * @return {*}
 */
app.get('/setcity', async (req, res) => {
  if (req.url.split('?').length < 2) {
    logger.error('收到更新城市请求，但参数错误');
    res.send('error');
  }
  if ((systemSetting.online = true)) {
    await read_data();
  }
  let city = decodeURIComponent(req.url.split('?')[1]);
  logger.log(`收到更新城市请求，当前所在城市更新为${city}`);
  if (city != userPublic.city) {
    userPublic.city = city;
    saveUserData();
    let msg = await getWeather();
    if (msg.code != 200) {
      logger.warn(
        `城市更新后的首次天气数据请求失败，可能是城市名，城市名应当为“北京”或“浙江杭州”，可手动访问检查：weathernew.pae.baidu.com/weathernew/pc?query=${city}天气&srcid=4982`
      );
    }
    res.send(msg);
  }
});

app.get('/weather', async (req, res) => {
  fs.readFile(path.join(systemSetting.root, './tmp/weather.json'), 'utf-8', async (err, dataStr) => {
    if (err) {
      logger.warn('天气数据本地缓存读取失败，试图访问百度中。');
      res.send('');
      getWeather();
    } else {
      res.send(dataStr);
      if (userTemp.weatherUpdateTime == null || Math.abs(new Date().getTime() - userTemp.weatherUpdateTime) > 900000) {
        //方法一：访问百度
        getWeather();
      }
    }
  });
});

async function getZhNews() {
  let tar = systemSetting.zhnew;
  let webs = []; //网址清单，内部元素不可重复，避免重复访问同一网页
  let spykey = []; //网页的爬虫关键词，二维数组，第一维是网址，第二维是关键词
  let sourcename = []; //来源，二维数组，第一维是网址，第二维是来源名称
  for (let i = 0; i < tar.length; i++) {
    let flag = true;
    for (let web_index = 0; web_index < webs.length; web_index++) {
      if (tar[i]['src'] == webs[web_index]) {
        flag = false;
        spykey[web_index].push(tar[i]['spykey']);
        sourcename[web_index].push(tar[i]['name']);
        break;
      }
    }
    if (flag) {
      webs.push(tar[i]['src']);
      spykey.push([tar[i]['spykey']]);
      sourcename.push([tar[i]['name']]);
    }
  }
  let finaljson = { time: new Date().getTime(), list: [] };
  // 启动浏览器
  const browser = await puppeteer.launch({
    headless: true,
    executablePath: systemSetting.browser_path,
  });

  //按webs数组中的网址顺序爬取
  for (let web_index = 0; web_index < webs.length; web_index++) {
    try {
      //
      const page = await browser.newPage();
      page.setDefaultNavigationTimeout(15000);
      await page.setViewport({ width: 1280, height: 800 });
      await page.setUserAgent(
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36'
      );

      await page.goto(webs[web_index], { waitUntil: 'networkidle2' });
      const data = await page.content();
      const $ = cheerio.load(data, { decodeEntities: false });

      let webhtml;
      if (webs[web_index].includes('tophub.today')) {
        webhtml = $('.bc-cc:first');
      } else if (webs[web_index].includes('www.jiqizhixin.com')) {
        webhtml = $;
      } else if (webs[web_index].includes('hub.baai.ac.cn/papers?')) {
        webhtml = $('.paper-list:first');
      } else if (webs[web_index].includes('hub.baai.ac.cn')) {
        webhtml = $('.story-list:first');
      } else if (webs[web_index].includes('www.aibase.com')) {
        webhtml = $('.grid').eq(1);
      } else {
        await page.close();
        continue;
      }

      //按spykey数组中的关键词顺序爬取
      for (let num = 0; num < spykey[web_index].length; num++) {
        let webjson = {
          name: sourcename[web_index][num],
          src: webs[web_index],
          spykey: spykey[web_index][num],
        };
        let news = [];
        if (webs[web_index].includes('tophub.today')) {
          let tar = webhtml.find(spykey[web_index][num]).find('.nano-content:first').children().first();
          let cnt = 0;
          while (cnt++ < Math.min(10, webhtml.find(spykey[web_index][num]).find('.nano-content:first').children().length)) {
            news.push({
              id: tar.find('.s').text().trim(),
              title: tar.find('.t').text().trim(),
              link: tar.attr('href'),
            });
            tar = tar.next();
          }
        } else if (webs[web_index].includes('www.jiqizhixin.com')) {
          //保存到本地
          fs.writeFile(path.join(systemSetting.root, './tmp/jiqizhixin.html'), webhtml.html(), { encoding: 'utf8' }, err => {
            if (err) {
              logger.error('jiqizhixin.html写入失败');
            }
          });
          continue;
          // let tar = webhtml.find('.home__center-left__list').children().first();
          // tar.next();
          // let cnt = 0;
          // while (cnt++ < Math.min(10, webhtml.children().length-1)) {
          //   news.push({
          //     id: cnt,
          //     title: tar.find('.home__article-item').text().trim(),
          //     link: 'https://www.jiqizhixin.com' + tar.find('.article-item__title').attr('href'),
          //   });
          //   tar = tar.next();
          // }
        } else if (webs[web_index].includes('hub.baai.ac.cn/papers?')) {
          let tar = webhtml.children().first();
          let cnt = 0;
          while (cnt++ < Math.min(8, webhtml.children().length)) {
            news.push({
              id: cnt,
              title: tar.find('.paper-item-summary').text().trim().slice(0, 40) + '...',
              link: 'https://hub.baai.ac.cn' + tar.find('.paper-item-wrap').children().first().attr('href'),
            });
            tar = tar.next();
          }
        } else if (webs[web_index].includes('hub.baai.ac.cn')) {
          let tar = webhtml.children().first();
          let cnt = 0;
          while (cnt++ < Math.min(10, webhtml.children().length)) {
            news.push({
              id: cnt,
              title: tar.find('.story-item-head').text().trim(),
              link: 'https://hub.baai.ac.cn' + tar.find('a').attr('href'),
            });
            tar = tar.next();
          }
        }
        webjson['list'] = news;
        finaljson['list'].push(webjson);
      }
      await page.close();
    } catch (error) {
      logger.warn('网络异常，中文新闻拉取失败，异常如下：', error);
      await browser.close();
      return '';
    }
  }
  await browser.close();

  let ss = JSON.stringify(finaljson);
  fs.writeFile(path.join(systemSetting.root, './tmp/zh.json'), ss, { encoding: 'utf8' }, err => {
    if (err) {
      logger.error('zh.json写入失败');
    }
  });
  logger.log('中文新闻拉取成功');
  return ss;
}

app.get('/zhnew', async (req, res) => {
  fs.readFile(path.join(systemSetting.root, './tmp/zh.json'), 'utf-8', (err, dataStr) => {
    if (err) {
      logger.error('中文新闻读取失败，重建中...');
      res.send('');
      getZhNews();
    } else {
      res.send(dataStr);
      //如果当天未签到，则不更新
      let d = new Date();
      const today = d.getFullYear() + '-' + (d.getMonth() + 1) + '-' + d.getDate();
      let tar = JSON.parse(dataStr);
      if (
        userPublic.signFlag == today + '-1' &&
        (tar['time'] == null || Math.abs(new Date().getTime() - tar['time']) > 120000)
      ) {
        getZhNews();
      }
    }
  });
});

async function getEngNews() {
  try {
    let tar = systemSetting.engnew;
    let webs = []; //网址清单，内部元素不可重复，避免重复访问同一网页
    let spykey = []; //网页的关键词，二维数组，第一维是网址，第二维是关键词
    let magazinename = []; //杂志名，二维数组，第一维是网址，第二维是杂志名

    //读取eng.json配置，确定爬取的网站和杂志
    for (let i = 0; i < tar.length; i++) {
      let flag = true;
      for (let web_index = 0; web_index < webs.length; web_index++) {
        if (tar[i]['src'] == webs[web_index]) {
          flag = false;
          spykey[web_index].push(tar[i]['spykey']);
          magazinename[web_index].push(tar[i]['name']);
          break;
        }
      }
      if (flag) {
        webs.push(tar[i]['src']);
        spykey.push([tar[i]['spykey']]);
        magazinename.push([tar[i]['name']]);
      }
    }
    let finaljson = { time: new Date().getTime(), list: [] };
    //按webs数组中的网址顺序爬取
    for (let web = 0; web < webs.length; web++) {
      const { data } = await axios.get(webs[web], {
        headers: {
          'User-Agent':
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        },
      });
      const $ = cheerio.load(data, { decodeEntities: false });
      const webhtml = $('#page-visible-cards');
      //按spykey数组中的关键词顺序爬取
      for (let num = 0; num < spykey[web].length; num++) {
        let magazinejson = {
          name: magazinename[web][num],
          src: webs[web],
          spykey: spykey[web][num],
        };
        let news = [];
        let tar = webhtml.find(spykey[web][num]).find('.publisher-link').first();
        let cnt = 0;
        while (tar.length !== 0 && cnt++ < 10) {
          news.push({
            time: tar.find('.text').text().trim(),
            title: tar.find('.article-link').text().trim(),
            link: tar.find('.article-link').attr('href').trim(),
          });
          tar = tar.next();
        }
        magazinejson['list'] = news;
        finaljson['list'].push(magazinejson);
      }
    }
    let ss = JSON.stringify(finaljson);
    fs.writeFile(path.join(systemSetting.root, './tmp/eng.json'), ss, { encoding: 'utf8' }, err => {
      if (err) {
        logger.error('eng.json写入失败');
      }
    });
    logger.log('英文外刊拉取成功');
    return ss;
  } catch (error) {
    logger.warn('网络异常，英语外刊拉取失败，异常如下：', error);
    return '';
  }
}

app.get('/engnews', (req, res) => {
  fs.readFile(path.join(systemSetting.root, './tmp/eng.json'), 'utf-8', (err, dataStr) => {
    if (err) {
      logger.error('国际新闻读取失败，重建中...');
      res.send('');
      getEngNews();
    } else {
      res.send(dataStr);
      //如果当天未签到，则不更新
      let d = new Date();
      const today = d.getFullYear() + '-' + (d.getMonth() + 1) + '-' + d.getDate();
      let tar = JSON.parse(dataStr);
      if (
        userPublic.signFlag == today + '-1' &&
        (tar['time'] == null || Math.abs(new Date().getTime() - tar['time']) > 1800000)
      ) {
        getEngNews();
      }
    }
  });
});

app.post('/post/signIn', async (req, res) => {
  const body = req.body;
  if ((systemSetting.online = true)) {
    await read_data();
  }
  let time = req.url.split('?')[1];
  let d = new Date();
  let dd = d.getDate();
  let mm = d.getMonth() + 1;
  let yy = d.getFullYear();
  let today = yy + '-' + mm + '-' + dd;
  if (userPublic.signFlag == today + '-1' || userPublic.signFlag == today + '-2') {
    res.send({
      status: 400,
      msg: 'signIn已经签到，无法重复签到',
      data: body,
    });
    logger.error('收到signIn请求，但是signIn已经签到，无法重复签到');
    return;
  }
  userPublic.signFlag = today + '-1';
  userPublic.signInTime = time;
  userPublic.signCardInfo = '0';
  logger.info('signIn succeed', 'signInTime:' + time);
  res.send({
    status: 200,
    msg: 'signIn操作成功',
    data: body,
  });
  saveUserData();
});

app.post('/post/signOut', async (req, res) => {
  const body = req.body;
  if ((systemSetting.online = true)) {
    await read_data();
  }
  let time = req.url.split('?')[1];
  let d = new Date();
  let dd = d.getDate();
  let mm = d.getMonth() + 1;
  let yy = d.getFullYear();
  let today = yy + '-' + mm + '-' + dd;
  if (userPublic.signFlag == today + '-0' || userPublic.signFlag == today + '-2') {
    res.send({
      status: 400,
      msg: 'signOut已经签退，或者尚未签到',
      data: body,
    });
    return logger.error('收到signOut请求，但是signOut已经签退，或者尚未签到');
  }
  userPublic.signFlag = today + '-2';
  userPublic.signOutTime = time;

  logger.info('signOut succeed');
  logger.info('signOutTime:' + time);
  res.send({
    status: 200,
    msg: 'signOut操作成功',
    data: body,
  });
  userPublic.HCmoney += 200;
  logger.log('当前合成玉数量：' + userPublic.HCmoney);
  saveUserData();
});

app.get('/transformhcmoney', async (req, res) => {
  //将合成玉转换为至纯源石，2000:1，不足2000的保留
  if ((systemSetting.online = true)) {
    await read_data();
  }
  let hc = userPublic.HCmoney;
  let ss = Math.floor(hc / 2000);
  userPublic.sourceStone += ss;
  userPublic.HCmoney = hc % 2000;
  logger.info(`成功将【${hc}】合成玉转换为【${ss}】至纯源石`);
  res.send({
    status: 200,
    msg: '转换成功',
    ss: userPublic.sourceStone,
    hc: userPublic.HCmoney,
  });
  saveUserData();
});

app.get('/clearsourcestone', async (req, res) => {
  if ((systemSetting.online = true)) {
    await read_data();
  }
  logger.info(`成功清空【${userPublic.sourceStone}】至纯源石！`);
  userPublic.sourceStone = 0;
  res.send({
    status: 200,
    msg: '清空成功',
  });
  saveUserData();
});

app.get('/userinf', (req, res) => {
  res.status = 200;
  res.send(userPublic);
});

app.post('/post/writenote', (req, res) => {
  let message;
  if (req.url.split('?').length == 1) message = '';
  else message = req.url.split('?')[1];
  fs.writeFile(path.join(systemSetting.root, './data/noteText'), message, err => {
    if (err) {
      logger.error('笔记信息保存失败');
      return res.send('error');
    }
    let messages = decodeURIComponent(message).split("#@##@#");
    logger.log('笔记信息保存成功，信息如下所示：\n' + messages.join('\n------------------\n'));
    res.send('success');
  });
});

app.post('/post/readnote', (req, res) => {
  fs.readFile(path.join(systemSetting.root, './data/noteText'), 'utf-8', (err, dataStr) => {
    if (err) {
      return logger.error('笔记信息读取失败');
    }
    res.send(dataStr);
  });
});

app.post('/post/readweblog', (req, res) => {
  fs.readFile(path.join(systemSetting.root, './README.md'), 'utf-8', (err, dataStr) => {
    if (err) {
      return logger.error('网站日志读取失败');
    }
    res.send(dataStr);
  });
});

app.post('/post/getwebfolder', async (req, res) => {
  try {
    const data = await fsp.readFile('data/webfolder.json', 'utf-8');
    res.send(data);
  } catch (err) {
    logger.error('网页获取失败, ERROR ID:11001');
    res.send('[]');
  }
});

// 异步函数获取网页的图标并保存到本地，返回后缀成功，返回“”表示图标无法下载
async function getWebIcon(url, fileName) {
  try {
    // 使用已安装的Chrome浏览器
    const browser = await puppeteer.launch({
      headless: true,
      executablePath: systemSetting.browser_path,
    });
    const page = await browser.newPage();
    page.setDefaultNavigationTimeout(12000);
    await page.setViewport({ width: 1280, height: 800 });
    await page.setUserAgent(
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
    );
    await page.goto(url, { waitUntil: 'networkidle2' });
    const html = await page.content();
    let iconUrl;

    const $ = cheerio.load(html);
    let iconLink = $('link[rel="icon"]').first();
    if (iconLink.length > 0) {
      let href = iconLink.attr('href');
      if (!/^http/.test(href)) {
        href = new URL(href, url).href;
      }
      iconUrl = encodeURI(href);
    } else {
      // 如果没有找到<link>标签，尝试默认的favicon.ico
      iconUrl = encodeURI(new URL('/favicon.ico', url).href);
      logger.warn('未找到合适的图标网址，将选择默认网址');
    }
    logger.log('尝试访问图标网址：' + iconUrl);

    try {
      setTimeout(() => {
        browser.close();
      }, 12000); // 无论如何，12s后关闭网页
      // 处理data URL的情况
      if (iconUrl.startsWith('data:image')) {
        const base64Data = iconUrl.replace(/^data:image\/\w+;base64,/, '');
        const iconFilePath = path.join(systemSetting.root, 'data', 'website', `${fileName}.png`);
        await fs.promises.writeFile(iconFilePath, base64Data, 'base64');
        logger.log(`成功下载${fileName}图标到 ${iconFilePath}`);
        return 'png';
      }

      // 对于非data URL的情况
      const iconRequestPromise = new Promise((resolve, reject) => {
        let iconFound = false;
        page.goto(iconUrl);
        page.on('response', async response => {
          if (response.url() === iconUrl) {
            iconFound = true;
            resolve(response);
          }
        });
        // 设置超时时间，以防图标未被找到
        setTimeout(() => {
          if (!iconFound) {
            reject(new Error('未获取到图标资源.'));
          }
        }, 8000); // 超时时间为8秒
      });
      const iconResponse = await iconRequestPromise;
      if (iconResponse) {
        const iconBuffer = await iconResponse.buffer();
        let iconExtension = path.extname(new URL(iconUrl).pathname);
        if (iconExtension.split('?').length != 1) {
          iconExtension = iconExtension.split('?')[0];
        }
        const iconFilePath = path.join(systemSetting.root, 'data', 'website', `${fileName}${iconExtension}`);
        await fs.promises.writeFile(iconFilePath, iconBuffer);
        logger.log(`成功下载${fileName}图标到 ${iconFilePath}`);
        return iconExtension.slice(1);
      } else {
        logger.error('未找到图标');
        return '';
      }
    } catch (error) {
      logger.error('下载图标时发生错误:', error);
      return '';
    }
  } catch (error) {
    logger.error('获取图标地址时发生错误:', error);
    return '';
  }
}

app.post('/post/webfolder', express.json(), async (req, res) => {
  const recArray = req.body;
  try {
    let data = await fsp.readFile('data/webfolder.json', 'utf-8');
    data = JSON.parse(data);
    for (let i = 0; i < recArray[0].length; i++) {
      for (let j = 0; j < data.length; j++) {
        if (data[j].web_name == recArray[0][i].web_name) {
          data[j].clear = true;
          break;
        }
      }
      if (recArray[0][i].type != null && recArray[0][i].type != '') {
        try {
          fs.unlinkSync(path.join('./data/website/', `${recArray[0][i].web_name}.${recArray[0][i].type}`));
        } catch (error) {
          logger.info(`删除网页存储的${recArray[0][i].web_name}.${recArray[0][i].type}图标失败 ${error}`);
        }
      }
      logger.info(`成功删除网页链接 ${recArray[0][i].web_name}`);
    }
    let newdata = [];
    for (let i = 0; i < data.length; i++) {
      if (data[i].clear === undefined) {
        newdata.push(data[i]);
      }
    }
    data = newdata;
    for (let i = 0; i < recArray[1].length; i++) {
      const ans = await getWebIcon(recArray[1][i].url, recArray[1][i].web_name);
      if (ans !== '' && (recArray[1][i].type == null || recArray[1][i].type == '')) {
        recArray[1][i].type = ans;
      }
      data.push(recArray[1][i]);
      logger.info(`成功添加网页链接 ${recArray[1][i].web_name}`);
    }
    for (let i = 0; i < recArray[2].length; i++) {
      for (let j = 0; j < data.length; j++) {
        if (data[j].web_name == recArray[2][i].web_name) {
          data[j] = recArray[2][i];
          logger.info(`成功更新网页链接 ${recArray[2][i].web_name}`);
          break;
        }
      }
    }

    //重新排序，先按照tag(str)排序，再按照order(int)排序
    data.sort((a, b) => {
      if (a.tag == b.tag) {
        return a.order - b.order;
      } else {
        return a.tag.localeCompare(b.tag);
      }
    });

    //保存data到本地
    fs.writeFile('data/webfolder.json', JSON.stringify(data), err => {
      if (err) {
        logger.error('网页夹更新失败, ERROR ID:11002');
      }
    });
  } catch (err) {
    logger.error('网页夹更新失败, ERROR ID:11002');
  }
  res.send('ok');
});

// File cleanup function
async function cleanupUnreferencedFiles() {
  try {
    const assetDir = path.join(systemSetting.root, 'data/asset');
    
    // Check if asset directory exists
    if (!fs.existsSync(assetDir)) {
      logger.info('Asset directory does not exist, skipping cleanup');
      return;
    }

    // Get all files in asset directory
    const files = fs.readdirSync(assetDir);
    if (files.length === 0) {
      logger.info('No files in asset directory, skipping cleanup');
      return;
    }

    // Read noteText content to find referenced files
    let noteContent = '';
    try {
      noteContent = fs.readFileSync(path.join(systemSetting.root, 'data/noteText'), 'utf-8');
      // Decode URL-encoded content
      noteContent = decodeURIComponent(noteContent);
    } catch (err) {
      logger.warn('Failed to read noteText file for cleanup:', err.message);
      return;
    }

    // Find unreferenced files
    const unreferencedFiles = [];
    const fileStats = [];

    for (const file of files) {
      const filePath = path.join(assetDir, file);
      const stats = fs.statSync(filePath);
      
      // Check if file is referenced in noteText
      const isReferenced = noteContent.includes(`data\\asset\\${file}`) || 
                          noteContent.includes(`data/asset/${file}`);
      
      if (!isReferenced) {
        unreferencedFiles.push({
          name: file,
          path: filePath,
          mtime: stats.mtime
        });
      }
      
      fileStats.push({
        name: file,
        mtime: stats.mtime,
        referenced: isReferenced
      });
    }

    logger.info(`Asset cleanup check: ${files.length} total files, ${unreferencedFiles.length} unreferenced files`);

    // If unreferenced files exceed 32, delete the oldest ones
    if (unreferencedFiles.length > 32) {
      // Sort by modification time (oldest first)
      unreferencedFiles.sort((a, b) => a.mtime - b.mtime);
      
      const filesToDelete = unreferencedFiles.slice(0, unreferencedFiles.length - 32);
      
      for (const file of filesToDelete) {
        try {
          fs.unlinkSync(file.path);
          logger.info(`Deleted unreferenced file: ${file.name}`);
        } catch (err) {
          logger.error(`Failed to delete file ${file.name}:`, err.message);
        }
      }
      
      logger.info(`Cleanup completed: deleted ${filesToDelete.length} unreferenced files`);
    } else {
      logger.info('No cleanup needed: unreferenced files count is within limit');
    }

  } catch (err) {
    logger.error('Error during file cleanup:', err.message);
  }
}

// Start periodic cleanup - every 6 hours (6 * 60 * 60 * 1000 ms)
setInterval(cleanupUnreferencedFiles, 6 * 60 * 60 * 1000);

// Run initial cleanup after server starts (delayed by 1 minute to allow server to fully initialize)
setTimeout(cleanupUnreferencedFiles, 60 * 1000);

// Media upload API endpoint
app.post('/api/upload-media', upload.single('file'), (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        status: 400,
        error: '未检测到文件',
        code: 'NO_FILE'
      });
    }

    // Generate filename with timestamp + 4-digit random number
    const now = new Date();
    const timestamp = now.getFullYear().toString() +
      (now.getMonth() + 1).toString().padStart(2, '0') +
      now.getDate().toString().padStart(2, '0') +
      now.getHours().toString().padStart(2, '0') +
      now.getMinutes().toString().padStart(2, '0') +
      now.getSeconds().toString().padStart(2, '0');

    const randomNum = Math.floor(Math.random() * 10000).toString().padStart(4, '0');

    // Get file extension based on mimetype
    let extension = '';
    switch (req.file.mimetype) {
      case 'image/png':
        extension = 'png';
        break;
      case 'image/jpeg':
      case 'image/jpg':
        extension = 'jpg';
        break;
      case 'image/gif':
        extension = 'gif';
        break;
      case 'image/webp':
        extension = 'webp';
        break;
      default:
        extension = 'jpg'; // Default to jpg
    }

    const filename = `${timestamp}_${randomNum}.${extension}`;
    const filepath = path.join(systemSetting.root, 'data', 'asset', filename);

    // Save file to disk
    fs.writeFile(filepath, req.file.buffer, (err) => {
      if (err) {
        logger.error('媒体文件保存失败:', err);
        return res.status(500).json({
          status: 500,
          error: '文件保存失败',
          code: 'SAVE_ERROR'
        });
      }

      logger.info(`媒体文件上传成功: ${filename}, 大小: ${req.file.size} bytes`);

      // Return success response
      res.status(200).json({
        status: 200,
        filename: filename,
        mediaType: req.file.mimetype,
        size: req.file.size
      });
    });

  } catch (error) {
    logger.error('媒体上传处理错误:', error);
    res.status(500).json({
      status: 500,
      error: '服务器内部错误',
      code: 'INTERNAL_ERROR'
    });
  }
});

//错误级别中间件必须注册在所有路由之后，有next的中间件，否则就是路由，无论使用use、get、post
app.use((err, req, res, next) => {
  // Handle multer errors
  if (err instanceof multer.MulterError) {
    if (err.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({
        status: 400,
        error: '文件大小超过限制（最大10MB）',
        code: 'FILE_TOO_LARGE'
      });
    }
    return res.status(400).json({
      status: 400,
      error: '文件上传错误: ' + err.message,
      code: 'UPLOAD_ERROR'
    });
  }

  // Handle file filter errors
  if (err.message && err.message.includes('不支持的文件格式')) {
    return res.status(400).json({
      status: 400,
      error: err.message,
      code: 'UNSUPPORTED_FORMAT'
    });
  }

  logger.error('Error happens: ' + err.message);
  logger.error('Request is below:\n' + req.message);
  res.send('Error happens: ' + err.message);
});

/*读取用户数据 */
async function read_data() {
  try {
    const filePath = path.join(systemSetting.root, './data/user');
    const dataStr = await fsp.readFile(filePath, 'utf-8');
    const dataLines = dataStr.split('\n');

    userPublic.HCmoney = parseInt(dataLines[0]);
    userPublic.signFlag = dataLines[1];
    userPublic.signInTime = dataLines[2];
    userPublic.signOutTime = dataLines[3];
    userPublic.sourceStone = parseInt(dataLines[4]);
    userPrivate.signCard = dataLines[5];
    userPublic.signCardInfo = dataLines[6];
    userPublic.city = dataLines[7];
    logger.info(`成功读取user文件`);
  } catch (error) {
    logger.error('user文件读取失败，程序初始化失败:', error);
  }
}

read_data();
setInterval(() => {
  getWeather();
}, 1200000);
setInterval(() => {
  //如果当天未签到，则跳过
  let d = new Date();
  const today = d.getFullYear() + '-' + (d.getMonth() + 1) + '-' + d.getDate();
  if (userPublic.signFlag == today + '-1') {
    getEngNews();
  }
}, 3600000);
setInterval(() => {
  //如果当天未签到，则跳过
  let d = new Date();
  const today = d.getFullYear() + '-' + (d.getMonth() + 1) + '-' + d.getDate();
  if (userPublic.signFlag == today + '-1') {
    getZhNews();
  }
}, 1200000);
