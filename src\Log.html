<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>开发日志</title>
  <link rel="shortcut icon" href="/images/favicon.ico">
  <link rel="stylesheet" type="text/css" href="/src/css/log.css">
  <link rel="stylesheet" type="text/css" href="/src/css/websiteFrame.css">
  <link rel="stylesheet" type="text/css" href="/src/css/font awesome/all.min.css">

</head>

<body>
  <div id="headMenu">
    <nav id="headMenuNav">
      <ul id="headMenuLeft">
        <li><a href="/" class="thisWebsiteIcon"></a></li>
        <li class="headMenuElement" @mouseenter="openWebFolder()" @mouseleave="closeWebFolder()">
          <a href="webfolder" class="headMenuLink unChoose">网页夹 <i id="webMenuBar" class="fa-solid"
              :class="{'fa-caret-up':webBarIsClose,'fa-caret-down':!webBarIsClose}"></i></a>
          <div id="webFolderDropMenu"
            :style="{opacity:webFolderOpacity,visibility:webFolderVisibility,top:webFolderTop}">
            <ul id="webFolderList1">
              <li class="webFolderItem" @mouseover="webFolderHover(index)" v-for="(webFolderItem,index) in webFolder">
                {{webFolderItem[0].tag}}</li>
            </ul>
            <ul id="webFolderList2">
            </ul>
          </div>
        </li>
        <li class="headMenuElement"><a href="https://kcn74j8y3msl.feishu.cn/wiki/I2TswJ7hQiAbC9khDCtcXlUInrg"
            class="headMenuLink unChoose">个人中心</a></li>
        <li class="headMenuElement"><a href="https://kcn74j8y3msl.feishu.cn/wiki/BiZMwIToAiGhaJkOmp8cbtNfnie" class="headMenuLink unChoose">工作记录</a></li>
        <li class="headMenuElement"><a href="https://weread.qq.com/" class="headMenuLink unChoose">微信读书</a></li>
      </ul>
      <div id="searchCh" class="unChoose none">
        <span id="searchChText" :style="{borderRadius:searchOptionBorderRadius,boxShadow:searchOptionBoxShadow}"
          @mouseenter="openSearchOption()" @mouseleave="closeSearchOption()">{{this.searchCurWeb}}
          <div id="searchChDropMenu"
            :style="{opacity:searchOptionOpacity,visibility:searchOptionVisibility,top:searchOptionTop}">
            <ul>
              <li class="searchChOp" @click="this.searchCurWeb='百度'">百度</li>
              <li class="searchChOp" @click="this.searchCurWeb='谷歌'">谷歌</li>
              <li class="searchChOp" @click="this.searchCurWeb='谷歌学术'">谷歌学术</li>
              <li class="searchChOp" @click="this.searchCurWeb='秘塔'">秘塔</li>
              <li class="searchChOp" @click="this.searchCurWeb='豆包'">豆包</li>
              <li class="searchChOp" @click="this.searchCurWeb='必应'">必应</li>
              <li class="searchChOp" @click="this.searchCurWeb='Github'">Github</li>
            </ul>
          </div>
        </span>
      </div>
      <div class="headMenuSearchForm none" @keydown="headInputKeyCheck"
        :style="{borderRadius:searchBorderRadius,boxShadow:searchBoxShadow}" @mouseenter="openSearchForm()"
        @mouseleave="closeSearchForm()">
        <input id="headMenuSearchInput" placeholder="立即搜索" type="text" maxlength="192" v-model="searchValue">
        <div id="searchWB" class="fa-solid fa-magnifying-glass" @click="searchWebsite()"></div>
        <div id="searchDropMenu" :style="{opacity:searchOpacity,visibility:searchVisibility,top:searchTop}"
          @mouseup.stop="">
          <ul id="searchSuggestList">
            <li v-for="suggestion in this.suggestFolderList"
              @click="this.searchValue ='[网页] '+ suggestion.text;this.searchWebsite();">
              <img class="searchWebIcon" :src=suggestion.d></img><em>{{suggestion.a}}<strong
                  class="searchMatchWord">{{suggestion.b}}</strong>{{suggestion.c}}</em>
            </li>
            <li v-for="suggestion in this.suggestHistoryList"
              @click="this.searchValue =suggestion.text;this.searchWebsite();">
              <strong>[历史] </strong><em>{{suggestion.a}}<strong
                  class="searchMatchWord">{{suggestion.b}}</strong>{{suggestion.c}}</em>
            </li>
            <li v-for="suggestion in this.suggestWebList" @click="this.searchValue = suggestion;this.searchWebsite();">
              {{suggestion}}</li>
          </ul>
          <span class="searchHistory"
            v-if="this.suggestFolderList.length+this.suggestHistoryList.length+this.suggestWebList==0">
            <button v-for="(history,index) in searchHistoryList" class='searchHistoryItem' draggable="true"
              @dragstart="dragItem" type='button' @click='clickSearchHis(history)' :title=history
              @mouseover="this.showHistoryIconList[index]=true" @mouseout="this.showHistoryIconList[index]=false">
              <div class='searchHistoryText'>{{history}}</div>
              <i class='closeIcon fa-solid fa-circle-xmark' :class="{'none':!this.showHistoryIconList[index]}"
                @click.stop="" @mouseup.stop="deleteHistory(index)"></i>
            </button>
            <div class='noHistoryHint' :class="{'none':this.hasHistory}">暂无历史记录</div>
          </span>
          <div id="searchBottomBox">
            <button id="clearHistoryBtn" type="button" @click="clearHistory()"
              v-if="this.suggestFolderList.length+this.suggestHistoryList.length+this.suggestWebList==0">清空搜索历史</button>
            <button id="switchSuggestBtn" type="button"
              @click="switchSuggest()">{{suggestEnable?"关闭云端搜索联想":"启用云端搜索联想"}}</button>
          </div>
        </div>
      </div>
      <span class="unChoose" id="timeText" @mouseenter="enterTimeText()"
        @mouseleave="leaveTimeText()">{{timeText}}</span>
      <span id="calendarDropMenu" :style="{opacity:calendarOpacity,visibility:calendarVisibility,right:calendarPos}"
        @mouseleave="leaveCalendar()">
        <div id="calendarTimeText" :style="{padding:calendarTimePad,fontWeight:calendarTimeSize}">{{timeText}}</div>
        <table id="calendarTable" align="center" border="0" cellpadding="4" cellspacing="0" width="100%">
          <div id="calendarMonthBg" class="unChoose"></div>
          <thead>
            <tr>
              <th>一</th>
              <th>二</th>
              <th>三</th>
              <th>四</th>
              <th>五</th>
              <th>六</th>
              <th>日</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
            </tr>
            <tr>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
            </tr>
            <tr>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
            </tr>
            <tr>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
            </tr>
            <tr>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
            </tr>
            <tr>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
              <td class="calendarDay"></td>
            </tr>
          </tbody>
        </table>
      </span>
    </nav>
    <div class="background"></div>
  </div>
  <div id="backImage"></div>
  <div class="mainBody">
    <div class="mainWindow">
      <div class="mainBox">
        <div>
          <span class="logTitle">网页开发日志</span>
          <span class="logSubTitle"></span>
        </div>
        <hr>
        <div class="logText">
        </div>
      </div>
    </div>

  </div>
  <script src="/src/js/src/util.js"></script>
  <script src="/src/js/src/vue.global.js"></script>
  <script src="/src/js/src/marked.min.js"></script>
  <script src="/node_modules/pinyin-pro/dist/index.js"></script>
  <script src="/src/js/PinyinCache.js"></script>
  <script src="/src/js/PinyinMatcher.js"></script>
  <script src="/src/js/websiteFrame.js"></script>
</body>

</html>

<script>
  function main() {
    setText();
  }


  function setText() {
    let loghttp = new XMLHttpRequest();
    loghttp.open("POST", "http://127.0.0.1:6699/post/readweblog", true);
    loghttp.send();
    loghttp.onreadystatechange = function () {
      if (loghttp.readyState == 4 && loghttp.status == 200) {
        const ans = loghttp.responseText;
        document.getElementsByClassName("logSubTitle")[0].innerText = ans.split("\n")[2];
        document.getElementsByClassName("logText")[0].innerHTML = marked.parse(ans.split("---")[1]);
      }
    }
  }

  window.onload = main;
</script>