{
  "git.ignoreLimitWarning": true,
  "editor.tabSize": 2,
  "editor.detectIndentation": false,
  "todo-tree.highlights.customHighlight": {
    "TODO": {
      "icon": "check",
      "type": "text-and-comment",
      "foreground": "#ffffff",
      "iconColour": "#ffbb00",
      "background": "#ffbb00",
      "opacity": 85,
    },
    "BUG": {
      "icon": "bug",
      "type": "line",
      "foreground": "#ffffff",
      "iconColour": "#ee0000",
      "background": "#ee0000",
      "opacity": 85,
    },
  },
  "todo-tree.filtering.excludeGlobs": [
    "**/nssm-2.24/**",
    "**/node_modules/**",
    "**/.vscode/**",
    "**/vue.global.js",
    "**/images/**",
  ],
  "kiroAgent.configureMCP": "Disabled"
}