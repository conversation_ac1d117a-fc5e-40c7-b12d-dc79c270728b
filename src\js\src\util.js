/**
 * 模拟cubic-bezier(0.42,0,0.58,1)的贝塞尔曲线，用于实现ease-in-out动画效果
 * @param {*} t 0-1之间
 * @returns 0-1之间的值
 */
function easeInOut(t) {
  let P0 = {x: 0, y: 0};
  let P1 = {x: 0.42, y: 0};
  let P2 = {x: 0.58, y: 1};
  let P3 = {x: 1, y: 1};
  return Math.pow(1 - t, 3) * P0.y + 3 * Math.pow(1 - t, 2) * t * P1.y + 3 * (1 - t) * Math.pow(t, 2) * P2.y + Math.pow(t, 3) * P3.y;
}

/**
 * 模拟cubic-bezier(0,0,0.58,1)的贝塞尔曲线，用于实现ease-in动画效果
 * @param {*} t 0-1之间
 * @returns 0-1之间的值
 */
function easeIn(t){
  let P0 = {x: 0, y: 0};
  let P1 = {x: 0.42, y: 0};
  let P2 = {x: 1, y: 1};
  return Math.pow(1 - t, 3) * P0.y + 3 * Math.pow(1 - t, 2) * t * P1.y + Math.pow(t, 3) * P2.y;
}

/**
 * 模拟cubic-bezier(0.42,0,1,1)的贝塞尔曲线，用于实现ease-out动画效果
 * @param {*} t 0-1之间
 * @returns 0-1之间的值
 */
function easeOut(t){
  let P0 = {x: 0, y: 0};
  let P1 = {x: 0.58, y: 1};
  let P2 = {x: 1, y: 1};
  return Math.pow(1 - t, 3) * P0.y + 3 * (1 - t) * Math.pow(t, 2) * P1.y + Math.pow(t, 3) * P2.y;  
}


/**
 * 实现对象淡出效果，最后会添加hide标签
 * @param {*} target 指向淡出对象
 * @param {int} time 淡出时间，默认100ms，为0时立即消失
 * @param {function} callback 淡出后执行该函数
 * @param {string} way 淡出方式，ease-in-out、ease-in、ease-out、linear，默认ease-in-out
 */
function fade_out(target, time = 100, callback, way) {
  if (target == null) return null;
  if ( target.getAttribute("fade_anima")!=null){
    clearInterval(target.getAttribute("fade_anima"));
    target.removeAttribute("fade_anima");
  }
  if (time > 0) {
    let cnt = 1.0;
    target.style.opacity = cnt;
    target.classList.remove("hide");
    const fade_out_action = setInterval(() => {
      cnt -= 16.0 / time;
      if (cnt <= 0.0) {
        target.style.opacity = 0;
        target.classList.add("hide");
        clearInterval(fade_out_action);
        if(target.getAttribute("fade_anima")==fade_out_action.toString()){
          target.removeAttribute("fade_anima", fade_out_action);
        }
        if (callback != null) callback();
      }
      else {
        switch(way){
          case "ease-in":target.style.opacity = easeIn(cnt);break;
          case "ease-out":target.style.opacity = easeOut(cnt);break;
          case "linear":target.style.opacity = cnt;break;
          default:target.style.opacity = easeInOut(cnt);break;
        }
      }
    }, 16)
    target.setAttribute("fade_anima", fade_out_action);
  }
  else if (time == 0) {
    target.style.opacity = 0;
    target.classList.add("hide");
    if (callback != null) callback();
  }
}


/**
 * 实现对象淡入效果，会先删除hide标签
 * @param {*} target 指向淡入对象
 * @param {int} time 淡入时间，默认350ms，为0时立即显示
 * @param {function} callback 淡出后执行该函数，如果time为0则立即执行
 * @param {string} way 淡入方式，ease-in-out、ease-in、ease-out、linear，默认ease-in-out
 */
function fade_in(target, time = 350, callback, way) {
  if (target == null) return null;
  if (target.getAttribute("fade_anima")!=null){
    clearInterval(target.getAttribute("fade_anima"));
    target.removeAttribute("fade_anima");
  }
  if (time > 0) {
    let cnt = 0.0;
    target.style.opacity = cnt;
    target.classList.remove("hide");
    const fade_in_action = setInterval(() => {
      cnt += 16.0 / time;
      if (cnt >= 1.0) {
        clearInterval(fade_in_action);
        if(target.getAttribute("fade_anima")==fade_in_action.toString()){
          target.removeAttribute("fade_anima", fade_in_action);
        }
        target.style.opacity = 1;
        if (callback != null) callback();
      }
      else {
        switch(way){
          case "ease-in":target.style.opacity = easeIn(cnt);break;
          case "ease-out":target.style.opacity = easeOut(cnt);break;
          case "linear":target.style.opacity = cnt;break;
          default:target.style.opacity = easeInOut(cnt);break;
        }
      }
    }, 16)
    target.setAttribute("fade_anima", fade_in_action);
  }
  else if (time == 0) {
    target.classList.remove("hide");
    target.style.opacity = 1;
    if (callback != null) callback();
  }
}


/**
 * 将普通字符串转换为浏览器HTML中可以存储的字符串，该方法只对特殊字符进行编码
 * @param {string} input 需要进行编码的字符串
 * @returns 编码之后的字符串
 */
function encodeHtmlChar(input) {
  if (input == null) return null;
  let ans = "";
  for (let i = 0; i < input.length; i++) {
    switch (input[i]) {
      case ' ': ans += "&nbsp;"; break;
      case '<': ans += "&lt;"; break;
      case '>': ans += "&gt;"; break;
      case '&': ans += "&amp;"; break;
      case '\'': ans += "&#39;"; break;
      case '\"': ans += "&quot;"; break;
      default: ans += input[i]; break;
    }
  }
  return ans;
}


/**
 * 将HTML存储的字符串中的特殊字符解码，得到普通字符串。需要注意的是该方法只对常用字符串解码，且存在漏洞，
 * 字符编码是同步的，这里的解码却是异步的，可能导致多次解码，实现反编码
 * @param {string} input 需要进行解码的字符串
 * @returns 解码之后的字符串
 */
function decodeHtmlChar(input) {
  if (input == null) return null;
  let s = '';
  if (input.length == 0) {
    return '';
  }
  s = input.replace(/&lt;/g, '<');
  s = s.replace(/&gt;/g, '>');
  s = s.replace(/&nbsp;/g, ' ');
  s = s.replace(/&#39;/g, '\'');
  s = s.replace(/&quot;/g, '\"');
  s = s.replace(/&amp;/g, '&');
  return s;
}

/**
 * 将1位的数字增加前置0，变成2位格式数字
 * @param {*} num 输入的数字
 * @returns 处理后的两位字符或数字
 */
function add_zero(num) {
  if (num < 10) return "0" + num;
  else return num;
}

function openLink(target) {
  //在本页面打开链接
  if(target==null||target=="")return;
  if (!target.startsWith("http://") && !target.startsWith("https://")) {
    window.open("http://" + target, "_blank");
  }
  else
    window.open(target, "_blank");
}

/**
 * 性能优化工具类
 * 提供防抖、节流等性能优化功能
 */
class PerformanceUtils {
  /**
   * 防抖函数 - 在事件被触发n秒后再执行回调，如果在这n秒内又被触发，则重新计时
   * @param {Function} func - 要防抖的函数
   * @param {number} delay - 延迟时间（毫秒）
   * @param {boolean} immediate - 是否立即执行第一次调用
   * @returns {Function} 防抖后的函数
   */
  static debounce(func, delay, immediate = false) {
    let timeoutId;
    let lastCallTime;
    
    return function debounced(...args) {
      const context = this;
      const callNow = immediate && !timeoutId;
      
      // 清除之前的定时器
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
      
      // 记录调用时间
      lastCallTime = Date.now();
      
      // 设置新的定时器
      timeoutId = setTimeout(() => {
        timeoutId = null;
        if (!immediate) {
          func.apply(context, args);
        }
      }, delay);
      
      // 如果是立即执行模式且是第一次调用，立即执行
      if (callNow) {
        func.apply(context, args);
      }
    };
  }

  /**
   * 节流函数 - 规定在一个单位时间内，只能触发一次函数
   * @param {Function} func - 要节流的函数
   * @param {number} delay - 节流间隔（毫秒）
   * @returns {Function} 节流后的函数
   */
  static throttle(func, delay) {
    let lastCallTime = 0;
    let timeoutId;
    
    return function throttled(...args) {
      const context = this;
      const now = Date.now();
      
      if (now - lastCallTime >= delay) {
        // 可以立即执行
        lastCallTime = now;
        func.apply(context, args);
      } else {
        // 需要等待，清除之前的定时器并设置新的
        if (timeoutId) {
          clearTimeout(timeoutId);
        }
        
        timeoutId = setTimeout(() => {
          lastCallTime = Date.now();
          func.apply(context, args);
        }, delay - (now - lastCallTime));
      }
    };
  }


  /**
   * 批量处理数组，避免长时间阻塞主线程
   * @param {Array} items - 要处理的项目数组
   * @param {Function} processor - 处理函数
   * @param {number} batchSize - 每批处理的数量
   * @param {number} delay - 批次间的延迟（毫秒）
   * @returns {Promise} 处理完成的Promise
   */
  static async batchProcess(items, processor, batchSize = 50, delay = 10) {
    const results = [];
    
    for (let i = 0; i < items.length; i += batchSize) {
      const batch = items.slice(i, i + batchSize);
      
      // 处理当前批次
      for (const item of batch) {
        try {
          const result = await processor(item);
          results.push(result);
        } catch (error) {
          console.warn('Batch process error:', error);
          results.push(null);
        }
      }
      
      // 如果不是最后一批，等待一段时间
      if (i + batchSize < items.length) {
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
    
    return results;
  }

  /**
   * 测量函数执行时间
   * @param {Function} func - 要测量的函数
   * @param {string} label - 标签名称
   * @returns {Function} 包装后的函数
   */
  static measureTime(func, label = 'Function') {
    return function measured(...args) {
      const startTime = performance.now();
      const result = func.apply(this, args);
      const endTime = performance.now();
      
      console.log(`${label} execution time: ${(endTime - startTime).toFixed(2)}ms`);
      
      return result;
    };
  }

  /**
   * 创建一个简单的性能监控器
   * @param {string} name - 监控器名称
   * @returns {Object} 监控器对象
   */
  static createPerformanceMonitor(name) {
    const stats = {
      calls: 0,
      totalTime: 0,
      minTime: Infinity,
      maxTime: 0,
      avgTime: 0
    };
    
    return {
      start() {
        this.startTime = performance.now();
      },
      
      end() {
        if (this.startTime) {
          const duration = performance.now() - this.startTime;
          stats.calls++;
          stats.totalTime += duration;
          stats.minTime = Math.min(stats.minTime, duration);
          stats.maxTime = Math.max(stats.maxTime, duration);
          stats.avgTime = stats.totalTime / stats.calls;
          this.startTime = null;
        }
      },
      
      getStats() {
        return {
          name,
          ...stats,
          minTime: stats.minTime === Infinity ? 0 : stats.minTime
        };
      },
      
      reset() {
        stats.calls = 0;
        stats.totalTime = 0;
        stats.minTime = Infinity;
        stats.maxTime = 0;
        stats.avgTime = 0;
      }
    };
  }
}
