const navApp = Vue.createApp({
  data() {
    return {
      LastFocus: null,
      searchCurWeb: '百度',
      timeText: '',
      hcMoney: 0,
      sourceStone: 0,
      webFolder: [], //网页夹原始数据，用于构建网页夹
      webFolderText: [], //网页夹纯文本，一维数组，用于搜索匹配字符串
      webFolderTimeout: null,
      webFolderAnime: null,
      webFolderOpacity: 0,
      webFolderVisibility: 'hidden',
      webBarIsClose: true,
      webFolderTop: '-2px',
      searchHistoryList: [],
      showHistoryIconList: [],
      hasHistory: false,
      searchOptionAnime: null,
      searchOptionTimeout: null,
      searchOptionOpacity: 0,
      searchOptionBoxShadow: '',
      searchOptionVisibility: 'hidden',
      searchOptionBorderRadius: '16px 0px 0px 16px',
      searchOptionTop: '47px',
      calendarOpacity: 0,
      calendarVisibility: 'hidden',
      calendarAnime: null,
      calendarTimeout: null,
      calendarPos: '5px',
      calendarTimeSize: '17px',
      calendarTimePad: '0 10px 0 0',
      searchBorderRadius: '0px 16px 16px 0px',
      searchBoxShadow: '',
      searchOpacity: 0,
      searchVisibility: 'hidden',
      searchTop: '47px',
      searchBoxAnime: null,
      searchBoxTimeout: null,
      suggestHistoryList: [], //搜索词在搜索历史的匹配结果
      suggestFolderList: [], //搜索词在网页夹的匹配结果
      suggestWebList: [], //搜索词在百度搜索的匹配结果
      webSuggestDelayTimer: null, //百度建议搜索延迟定时器，输入框一段时间未改变时才会发起请求
      webSuggestExceedTimer: null, //超时定时器，请求超时时会将script标签删除
      localSuggestDelayTimer: null, //本地匹配检索延迟定时器，输入框一段时间未改变时才会发起请求
      suggestEnable: false, //是否启用云端搜索联想
      searchValue: '', //与输入框文本双向绑定，用于显示和搜索提交
      curInputText: '', //记录用户最后输入在搜索框的有效内容
      curSuggestIndex: 0, //0表示未选中
    };
  },
  mounted() {
    document.onmouseup = this.mouseFocus;
    window.onscroll = this.updateNav;
    window.searchCallBack = this.searchCallBack;
    this.updateNav();
    setInterval(this.getCurDate, 500);
    this.getCurDate();
    this.initCalendar();
    this.initSearchHistory();
    this.initWebsiteFolder();
    this.getTheme();
    this.bindSearchSuggestion();
    
    this.debouncedLocalSearch = PerformanceUtils.debounce(
      this.triggerLocalSearchSuggestion.bind(this), 200
    );
    this.debouncedWebSearch = PerformanceUtils.debounce(
      this.triggerWebSearchSuggestion.bind(this), 600
    );
    let xmlhttp = new XMLHttpRequest();
    xmlhttp.open('GET', 'http://127.0.0.1:6699/userinf', true);
    xmlhttp.send();
    xmlhttp.onload = () => {
      if (xmlhttp.readyState == 4 && xmlhttp.status == 200) {
        let map = JSON.parse(xmlhttp.responseText);
        for (let item in map) {
          localStorage.setItem(item.toString(), map[item].toString());
        }
        this.hcMoney = localStorage.getItem('HCmoney');
        this.sourceStone = localStorage.getItem('sourceStone');
      }
    };
    //判断是否签到，未签到隐藏搜索框
    sign_status = localStorage.getItem('signFlag');
    let d = new Date();
    const today = d.getFullYear() + '-' + (d.getMonth() + 1) + '-' + d.getDate();
    if (sign_status == today + '-1') {
      document.getElementById('searchCh').classList.remove('none');
      document.getElementsByClassName('headMenuSearchForm')[0].classList.remove('none');
    }
  },
  methods: {
    /**
     * 跟踪鼠标聚焦元素，鼠标最新选中的元素拥有class“focusNow”
     * @returns {boolean} 表示聚焦元素是否切换
     */
    mouseFocus(event) {
      const newE = document.elementFromPoint(event.clientX, event.clientY);
      if (newE == this.LastFocus || newE == null) return false;
      if (this.LastFocus != null) {
        this.LastFocus.classList.remove('focusNow');
      }
      newE.classList.add('focusNow');
      this.LastFocus = newE;
    },

    /**
     * 清除当前聚焦元素的标记，为其他模块提供接口
     */
    clearMouseFocus() {
      if (this.LastFocus != null) {
        this.LastFocus.classList.remove('focusNow');
        this.LastFocus = null;
      }
    },

    /**
     * 网页搜索函数，根据搜索框内容打开新网址，调用其他函数保存搜索记录
     */
    searchWebsite() {
      let url = null;
      this.clearSearchSuggestion();
      if (this.searchValue.length != 0) {
        this.searchValue = this.searchValue.trim();
        if (this.searchValue.startsWith('[网页]')) {
          let webName = this.searchValue.slice(4).trim();
          for (let i = 0; i < this.webFolderText.length; i++) {
            if (this.webFolderText[i].name == webName) {
              url = this.webFolderText[i].link;
              break;
            }
          }
        } else {
          if (this.searchValue.startsWith('[历史]')) {
            this.searchValue = this.searchValue.slice(4).trim();
          }
          switch (this.searchCurWeb) {
            case '豆包':
            case 'DeepSeek':
              this.selectSuggestion(0);
              setTimeout(() => {
                this.searchValue = '';
                this.curInputText = '';
              }, 2000);
              return;
            case '百度':
              url = 'https://www.baidu.com/s?wd=';
              break;
            case '秘塔':
              url = 'https://metaso.cn/?q=';
              break;
            case '必应':
              url = 'https://www.bing.com/search?q=';
              break;
            case '谷歌':
              url = 'https://www.google.com/search?q=';
              break;
            case '谷歌学术':
              url = 'https://scholar.google.com.hk/scholar?hl=zh-CN&q=';
              break;
            case '知乎':
              url = 'https://www.zhihu.com/search?type=content&q=';
              break;
            case 'Github':
              url = 'https://github.com/search?q=';
              break;
          }
          url += encodeURIComponent(this.searchValue);
          this.saveSearchHistory(this.searchValue);
        }
      } else {
        switch (this.searchCurWeb) {
          case '百度':
            url = 'https://www.baidu.com/';
            break;
          case '秘塔':
            url = 'https://metaso.cn/';
            break;
          case '豆包':
            url = 'https://www.doubao.com/chat/';
            break;
          case 'DeepSeek':
            url = 'https://chat.deepseek.com/';
            break;
          case '必应':
            url = 'https://www.bing.com/';
            break;
          case '谷歌':
            url = 'https://www.google.com/';
            break;
          case '谷歌学术':
            url = 'https://scholar.google.com.hk/';
            break;
          case '知乎':
            url = 'https://www.zhihu.com/';
            break;
          case 'Github':
            url = 'https://github.com/';
            break;
        }
      }
      this.selectSuggestion(0);
      this.searchValue = '';
      this.curInputText = '';
      window.open(url, '_blank');
    },

    /**
     * 更新导航栏的函数，确保导航栏始终在顶部
     */
    updateNav() {
      const scrollTop = document.documentElement.scrollTop || document.body.scrollTop;
      const target = document.getElementById('headMenu').getElementsByClassName('background')[0];
      if (scrollTop > 25 && !target.classList.contains('sliceDown') && !target.classList.contains('sliceing')) {
        target.classList.add('sliceing');
        fade_out(target, 100, () => {
          target.classList.remove('sliceing');
          target.classList.add('sliceDown');
          fade_in(target, 150, null, 'ease-in-out');
        }, 'ease-in');
      } else if (scrollTop == 0 && target.classList.contains('sliceDown')) {
        fade_out(target, 100, () => {
          target.classList.remove('sliceDown');
          fade_in(target, 150, null, 'ease-in-out');
        }, 'ease-in');
      }
    },

    /**
     * 更新网页时间
     */
    getCurDate() {
      const d = new Date();
      const month = add_zero(d.getMonth() + 1);
      const days = add_zero(d.getDate());
      this.timeText = month + '月' + days + '日 ' + d.toLocaleTimeString();
    },

    /**
     * 打开文件夹时的动态效果
     */
    openWebFolder() {
      clearTimeout(this.webFolderTimeout);
      if (this.webFolderVisibility == 'visible') return;
      this.webFolderTimeout = setTimeout(() => {
        let cnt = 0;
        this.webFolderVisibility = 'visible';
        this.webFolderTop = '4px';
        clearInterval(this.webFolderAnime);
        this.webBarIsClose = false;
        this.webFolderAnime = setInterval(() => {
          cnt += 1;
          this.webFolderOpacity = easeInOut(cnt * 0.12);
          if (cnt == 9) {
            this.webFolderOpacity = 1;
            clearInterval(this.webFolderAnime);
          }
        }, 11);
      }, 150);
    },

    /**
     * 打开日历时的动态效果
     */
    enterTimeText() {
      clearTimeout(this.calendarTimeout);
      if (this.calendarVisibility == 'visible') return;
      this.calendarTimeout = setTimeout(() => {
        let cnt = 0;
        this.calendarVisibility = 'visible';
        clearInterval(this.calendarAnime);
        this.calendarAnime = setInterval(() => {
          cnt += 1;
          this.calendarOpacity = easeInOut(cnt * 0.12);
          if (cnt == 9) {
            this.calendarOpacity = 1;
            this.calendarTimePad = '4px 80px 0 0';
            this.calendarPos = '12px';
            this.calendarTimeSize = '18px';
            clearInterval(this.calendarAnime);
          }
        }, 11);
      }, 100);
    },

    closeWebFolder() {
      clearTimeout(this.webFolderTimeout);
      if (this.webFolderVisibility == 'hidden') return;
      this.webFolderTimeout = setTimeout(() => {
        this.webFolderTop = '-2px';
        let cnt2 = 0;
        clearInterval(this.webFolderAnime);
        this.webFolderAnime = setInterval(() => {
          cnt2 += 1;
          this.webFolderOpacity = easeInOut(1 - cnt2 * 0.08);
          if (cnt2 == 13) {
            this.webFolderOpacity = 0;
            this.webFolderVisibility = 'hidden';
            this.webBarIsClose = true;
            clearInterval(this.webFolderAnime);
          }
        }, 11);
      }, 250);
    },

    leaveTimeText() {
      clearTimeout(this.calendarTimeout);
      if (this.calendarVisibility == 'visible') return;
      this.calendarTimePad = '0 10px 0 0';
      this.calendarPos = '5px';
      this.calendarTimeSize = '17px';
      this.calendarTimeout = setTimeout(() => {
        if (this.calendarVisibility == 'visible') return;
        let cnt2 = 0;
        clearInterval(this.calendarAnime);
        this.calendarAnime = setInterval(() => {
          cnt2 += 1;
          this.calendarOpacity = easeInOut(1 - cnt2 * 0.08);
          if (cnt2 == 13) {
            this.calendarOpacity = 0;
            this.calendarVisibility = 'hidden';
            clearInterval(this.calendarAnime);
          }
        }, 11);
      }, 250);
    },

    leaveCalendar() {
      clearTimeout(this.calendarTimeout);
      this.calendarTimePad = '0 10px 0 0';
      this.calendarPos = '5px';
      this.calendarTimeSize = '17px';
      this.calenarTimeout = setTimeout(() => {
        let cnt2 = 0;
        clearInterval(this.calendarAnime);
        this.calendarAnime = setInterval(() => {
          cnt2 += 1;
          this.calendarOpacity = easeInOut(1 - cnt2 * 0.08);
          if (cnt2 == 13) {
            this.calendarOpacity = 0;
            this.calendarVisibility = 'hidden';
            clearInterval(this.calendarAnime);
          }
        }, 11);
      }, 250);
    },

    /**
     * 设置日历表，页面初始化时调用
     */
    initCalendar() {
      let date = new Date();
      let year = date.getFullYear();
      let day = date.getDate();
      let month = date.getMonth() + 1;
      document.getElementById('calendarMonthBg').innerText = month;
      let max_day;
      let number = (date.getDay() - day + 35) % 7; //计算1号在表格中的起始下标
      let target = document.getElementsByClassName('calendarDay');
      //计算本月的天数
      if (month == 1 || month == 3 || month == 5 || month == 7 || month == 8 || month == 10 || month == 12)
        max_day = 31;
      else if (month == 4 || month == 6 || month == 9 || month == 11) max_day = 30;
      else if (year % 100 != 0 && year % 4 == 0) max_day = 29;
      else max_day = 28;

      //更新日期每个元素的状态
      for (let i = 0; i < max_day; i++) {
        target[i + number].innerText = i + 1;
        if (i + 1 == day) target[i + number].classList.add('calendarToday');
        else target[i + number].classList.add('calendarThisMonth');
      }
      for (let index = 0; index < 42 - max_day - number; index++) {
        target[index + max_day + number].classList.add('calendarOtherMonth');
        target[index + max_day + number].innerText = index + 1;
      }
      month = month == 1 ? 12 : month - 1;
      let max_day_2;
      if (month == 1 || month == 3 || month == 5 || month == 7 || month == 8 || month == 10 || month == 12)
        max_day_2 = 31;
      else if (month == 4 || month == 6 || month == 9 || month == 11) max_day_2 = 30;
      else if (year % 100 != 0 && year % 4 == 0) max_day_2 = 29;
      else max_day_2 = 28;
      for (let i = number - 1; i >= 0; i--) {
        target[i].innerText = max_day_2--;
        target[i].classList.add('calendarOtherMonth');
      }
    },

    moneyHandle(type) {
      if (type == 0) {
        let xmlhttp = new XMLHttpRequest();
        xmlhttp.open('GET', 'http://127.0.0.1:6699/transformhcmoney', true);
        xmlhttp.send();
        xmlhttp.onload = () => {
          if (xmlhttp.readyState == 4 && xmlhttp.status == 200) {
            let msg = JSON.parse(xmlhttp.responseText);
            if (msg.status == 200) {
              this.hcMoney = msg.hc;
              this.sourceStone = msg.ss;
              localStorage.setItem('HCmoney', msg.hc);
              localStorage.setItem('sourceStone', msg.ss);
            }
          }
        };
      } else if (type == 1) {
        //后端代码
        let xmlhttp = new XMLHttpRequest();
        xmlhttp.open('GET', 'http://127.0.0.1:6699/clearsourcestone', true);
        xmlhttp.send();
        xmlhttp.onload = () => {
          if (xmlhttp.readyState == 4 && xmlhttp.status == 200) {
            let msg = JSON.parse(xmlhttp.responseText);
            if (msg.status == 200) {
              this.sourceStone = 0;
              localStorage.setItem('sourceStone', 0);
            }
          }
        };
      }
    },

    /**
     * 初始化文件夹系统
     */
    initWebsiteFolder() {
      let xmlhttp = new XMLHttpRequest();
      xmlhttp.open('POST', 'http://127.0.0.1:6699/post/getwebfolder', true);
      xmlhttp.onload = () => {
        if (xmlhttp.readyState == 4 && xmlhttp.status == 200) {
          let msg = JSON.parse(xmlhttp.responseText);
          this.webFolder = [];
          this.webFolderText = [];
          let box1 = [];
          let last = '';
          for (let i = 0; i < msg.length; i++) {
            if (msg[i].tag != last) {
              if (box1.length != 0) {
                this.webFolder.push(box1);
                box1 = [];
              }
              last = msg[i].tag;
            }
            box1.push(msg[i]);
          }
          if (box1.length != 0) {
            this.webFolder.push(box1);
            box1 = [];
          }
          for (let i = 0; i < this.webFolder.length; i++) {
            for (let j = 0; j < this.webFolder[i].length; j++) {
              if (this.webFolder[i][j].type == null || this.webFolder[i][j].type == '') {
                this.webFolder[i][j].src = '/images/default.png';
              } else {
                this.webFolder[i][j].src =
                  `/data/website/${this.webFolder[i][j].web_name}.${this.webFolder[i][j].type}`;
              }
              this.webFolderText.push({
                name: this.webFolder[i][j].web_name,
                link: this.webFolder[i][j].url,
                src: this.webFolder[i][j].src,
              });
            }
          }
          
          // 网页夹数据加载完成后，触发缓存预热
          this.$nextTick(() => {
            this.warmupPinyinCache();
          });
        }
      };
      xmlhttp.send();
    },

    webFolderHover(index) {
      const list2 = document.getElementById('webFolderList2');
      if (document.getElementsByClassName('webFolderItemHover').length != 0)
        document.getElementsByClassName('webFolderItemHover')[0].classList.remove('webFolderItemHover');
      document.getElementsByClassName('webFolderItem')[index].classList.add('webFolderItemHover');
      for (let i = list2.children.length - 1; i >= 0; i--) {
        list2.children[i].remove();
      }
      for (let i = 0; i < this.webFolder[index].length; i++) {
        if (this.webFolder[index][i].description == null || this.webFolder[index][i].description == '') {
          let tag =
            "<li class='webItem' onclick='openLink(\"" +
            this.webFolder[index][i].url +
            "\")'><img class='webItemIcon' src='" +
            this.webFolder[index][i].src +
            "'>" +
            this.webFolder[index][i].web_name +
            '</li>';
          list2.insertAdjacentHTML('beforeend', tag);
        } else {
          let tag =
            "<li class='webItem' title='" +
            this.webFolder[index][i].description +
            "' onclick='openLink(\"" +
            this.webFolder[index][i].url +
            "\")'><img class='webItemIcon' src='" +
            this.webFolder[index][i].src +
            "'>" +
            this.webFolder[index][i].web_name +
            '</li>';
          list2.insertAdjacentHTML('beforeend', tag);
        }
      }
    },

    /**
     * 获得当前主题颜色配置
     */
    getTheme() {
      let theme = localStorage.getItem('theme');
      let dayNight = localStorage.getItem('dayNight');
      if (theme == null) {
        localStorage.setItem('theme', 'FantasyDream');
        theme = 'FantasyDream';
      }
      if (dayNight == null) {
        localStorage.setItem('dayNight', 'day');
        dayNight = 'day';
      }
      let new_link = document.createElement('link');
      new_link.setAttribute('key', 'theme');
      new_link.setAttribute('rel', 'stylesheet');
      new_link.setAttribute('type', 'text/css');
      new_link.setAttribute('href', '/src/css/theme/' + theme + '.css');
      document.documentElement.setAttribute('theme', `${theme}-${dayNight}`);
      document.body.appendChild(new_link);
    },

    /**
     * 鼠标进入搜索选项时，显示搜索引擎选择菜单
     */
    openSearchOption() {
      clearTimeout(this.searchOptionTimeout);
      if (this.searchOptionVisibility == 'visible') return;
      if (this.searchOptionVisibility == 'visible') return;
      this.searchOptionTimeout = setTimeout(() => {
        let cnt = 0;
        this.searchOptionVisibility = 'visible';
        this.searchOptionTop = '44px';
        clearInterval(this.searchOptionAnime);
        this.searchOptionAnime = setInterval(() => {
          cnt += 1;
          this.searchOptionOpacity = easeInOut(cnt * 0.12);
          this.searchOptionBorderRadius = `16px 0 0 ${16 - (16 * cnt) / 9}px`;
          if (cnt == 9) {
            this.searchOptionOpacity = 1;
            clearInterval(this.searchOptionAnime);
          }
        }, 11);
      }, 150);
    },

    /**
     * 鼠标离开搜索选项时，显示搜索引擎选择菜单
     */
    closeSearchOption() {
      clearTimeout(this.searchOptionTimeout);
      if (this.searchOptionVisibility == 'hidden') return;
      this.searchOptionTimeout = setTimeout(() => {
        this.searchOptionTop = '47px';
        let cnt2 = 0;
        clearInterval(this.searchOptionAnime);
        this.searchOptionAnime = setInterval(() => {
          cnt2 += 1;
          this.searchOptionOpacity = easeInOut(1 - cnt2 * 0.08);
          this.searchOptionBorderRadius = `16px 0 0 ${(cnt2 / 13) * 16}px`;
          if (cnt2 == 13) {
            this.searchOptionOpacity = 0;
            this.searchOptionVisibility = 'hidden';
            clearInterval(this.searchOptionAnime);
          }
        }, 11);
      }, 250);
    },

    /**
     * 更新选中的搜索联想词，选中的联想词高亮
     * @param {number} status 1表示选择下一个，-1表示选择上一个，0表示重置不选择(输入框内容改变、更新联想词、关闭下拉框、搜索提交)
     */
    selectSuggestion(status) {
      const num = document.getElementById('searchSuggestList').children.length;
      const last = document.getElementsByClassName('sg-se')[0];
      if (num == 0) return;
      switch (status) {
        case 1:
          if (last != null) last.classList.remove('sg-se');
          this.curSuggestIndex = (this.curSuggestIndex + 1) % (num + 1);
          if (this.curSuggestIndex != 0) {
            let target = document.getElementById('searchSuggestList').children[this.curSuggestIndex - 1];
            target.classList.add('sg-se');
            this.searchValue =
              target.getElementsByClassName('searchWebIcon').length != 0
                ? '[网页] ' + target.innerText
                : target.innerText;
          } else {
            this.searchValue = this.curInputText;
          }
          break;
        case -1:
          if (last != null) last.classList.remove('sg-se');
          this.curSuggestIndex = (this.curSuggestIndex + num) % (num + 1);
          if (this.curSuggestIndex != 0) {
            let target = document.getElementById('searchSuggestList').children[this.curSuggestIndex - 1];
            target.classList.add('sg-se');
            this.searchValue =
              target.getElementsByClassName('searchWebIcon').length != 0
                ? '[网页] ' + target.innerText
                : target.innerText;
          } else {
            this.searchValue = this.curInputText;
          }
          break;
        case 0:
          this.curSuggestIndex = 0;
          if (last != null) last.classList.remove('sg-se');
      }
    },

    /**
     * 搜索框得到鼠标聚焦的下拉框动态效果函数
     */
    openSearchForm() {
      clearTimeout(this.searchBoxTimeout);
      if (this.searchVisibility == 'visible') return;
      if (this.searchVisibility == 'visible') return;
      this.searchBoxTimeout = setTimeout(() => {
        let cnt = 0;
        this.searchVisibility = 'visible';
        this.searchTop = '44px';
        clearInterval(this.searchBoxAnime);
        this.searchBoxAnime = setInterval(() => {
          cnt += 1;
          this.searchOpacity = easeInOut(cnt * 0.12);
          this.searchBorderRadius = `0 16px ${16 - (16 * cnt) / 9}px 0`;
          if (cnt == 9) {
            this.searchOpacity = 1;
            this.searchBoxShadow = '1px 3px 4px var(--windowShadowColor)';
            clearInterval(this.searchBoxAnime);
          }
        }, 11);
      }, 150);
    },

    /**
     * 搜索框失去鼠标聚焦的下拉框动态效果函数
     */
    closeSearchForm() {
      clearTimeout(this.searchBoxTimeout);
      if (this.searchVisibility == 'hidden') return;
      this.searchBoxTimeout = setTimeout(() => {
        let searchWait = setInterval(() => {
          if (
            document.getElementById('headMenuSearchInput').classList.contains('focusNow') == false &&
            document.getElementById('searchDropMenu').classList.contains('focusNow') == false
          ) {
            clearInterval(searchWait);
            clearInterval(this.searchBoxAnime);
            let cnt2 = 0;
            this.searchBoxShadow = '';
            this.searchTop = '47px';
            this.searchBoxAnime = setInterval(() => {
              cnt2 += 1;
              this.searchOpacity = easeInOut(1 - cnt2 * 0.08);
              this.searchBorderRadius = `0 16px ${(cnt2 / 13) * 16}px 0`;
              if (cnt2 == 13) {
                this.searchOpacity = 0;
                this.searchVisibility = 'hidden';
                clearInterval(this.searchBoxAnime);
              }
            }, 11);
          }
        }, 48);
      }, 250);
    },

    /**
     * 对搜索框的部分键特殊处理，按下回车就进行搜索，按上下键选择搜索联想词
     */
    headInputKeyCheck(e) {
      let code = e.keyCode;
      switch (code) {
        case 13:
          this.searchWebsite();
          break;
        case 38:
          e.preventDefault();
          this.selectSuggestion(-1);
          break;
        case 40:
          e.preventDefault();
          this.selectSuggestion(1);
          break;
        case 39:
        case 37:
          break;
        default:
          this.selectSuggestion(0);
          break;
      }
    },

    /**
     * 初始化搜索记录，从浏览器存储读取记录
     */
    initSearchHistory() {
      let number = 0;
      let message = localStorage.getItem('history0');
      this.searchHistoryList = [];
      this.showHistoryIconList = [];
      while (message != null) {
        number++;
        this.searchHistoryList.push(message); //history0是最新的记录
        this.showHistoryIconList.push(false);
        message = localStorage.getItem('history' + number);
      }
      if (number == 0) {
        this.hasHistory = false;
      } else {
        this.hasHistory = true;
      }
    },

    /**
     * 将搜索的记录存储在浏览器中，搜索记录已有时，将其移动到最前面，搜索记录超过20条时，删除最旧的记录
     * @param {string} text 要保存的搜索记录文本
     */
    saveSearchHistory(text) {
      if (text == '') return;
      const maxn = 20;
      for (let i = 0; i < this.searchHistoryList.length; i++) {
        if (this.searchHistoryList[i] == text) {
          for (let j = i; j > 0; j--) {
            localStorage.setItem('history' + j, localStorage.getItem('history' + (j - 1)));
          }
          localStorage.setItem('history0', text);
          this.searchHistoryList.splice(i, 1);
          this.searchHistoryList.unshift(text);
          return;
        }
      }
      if (this.searchHistoryList.length >= maxn) {
        for (let i = this.searchHistoryList.length - 1; i > 0; i--) {
          localStorage.setItem('history' + i, localStorage.getItem('history' + (i - 1)));
        }
        localStorage.setItem('history0', text);
        this.searchHistoryList.pop();
        this.searchHistoryList.unshift(text);
      } else {
        for (let i = this.searchHistoryList.length - 1; i >= 0; i--) {
          localStorage.setItem('history' + (i + 1), localStorage.getItem('history' + i));
        }
        localStorage.setItem('history0', text);
        this.searchHistoryList.unshift(text);
        this.showHistoryIconList.unshift(false);
      }
    },

    /**
     * 搜索记录按钮的鼠标点击事件
     * @param {*} history
     */
    clickSearchHis(history) {
      this.searchValue = history;
      this.searchWebsite();
    },

    /**
     * 删除某条搜索历史记录
     *  @param {*} index 要删除的记录对象下标
     */
    deleteHistory(index) {
      if (index < 0 || index > this.searchHistoryList.length - 1) return;
      for (let i = index; i < this.searchHistoryList.length - 1; i++) {
        localStorage.setItem('history' + i, localStorage.getItem('history' + (i + 1)));
      }
      localStorage.removeItem('history' + (this.searchHistoryList.length - 1));
      this.searchHistoryList.splice(index, 1);
      this.showHistoryIconList.splice(index, 1);
      if (this.searchHistoryList.length == 0) {
        this.hasHistory = false;
      }
    },

    /**
     * 清除搜索历史记录
     */
    clearHistory() {
      for (let i = 0; i < this.searchHistoryList.length; i++) {
        localStorage.removeItem('history' + i);
      }
      this.searchHistoryList = [];
      this.showHistoryIconList = [];
      this.hasHistory = false;
    },

    /**
     * 触发一次本地（网页夹+搜索历史）搜索联想
     */
    triggerLocalSearchSuggestion() {
      let lowerInputText = '';
      if (this.curInputText.startsWith('[网页]')) {
        lowerInputText = this.curInputText.slice(4).trim().toLowerCase();
      } else {
        lowerInputText = this.curInputText.toLowerCase();
      }
      if (lowerInputText.length >= 2) {
        this.suggestFolderList = [];
        this.suggestHistoryList = [];
        
        // 检测输入是否为拼音
        const isPinyinInput = PinyinConverter.isPinyin(lowerInputText);
        
        for (let i = 0; i < this.webFolderText.length; i++) {
          let matchResult = null;
          
          // 首先尝试传统的文本匹配
          const index = this.webFolderText[i].name.toLowerCase().indexOf(lowerInputText);
          if (index != -1) {
            matchResult = {
              a: this.webFolderText[i].name.slice(0, index),
              b: this.webFolderText[i].name.slice(index, index + lowerInputText.length),
              c: this.webFolderText[i].name.slice(index + lowerInputText.length),
              d: this.webFolderText[i].src,
              text: this.webFolderText[i].name,
              matchType: 'text',
              pinyinMatch: false,
              score: 100 // 文本匹配优先级最高
            };
          }
          // 如果没有文本匹配且输入是拼音，尝试拼音匹配
          else if (isPinyinInput) {
            // 尝试全拼匹配
            const fullMatch = PinyinMatcher.fullMatch(lowerInputText, this.webFolderText[i].name);
            if (fullMatch) {
              matchResult = this._convertPinyinMatchToSuggestion(fullMatch, this.webFolderText[i], 'pinyin-full');
            }
          }
          if (matchResult) {
            this.suggestFolderList.push(matchResult);
          }
        }
        
        // 按匹配得分排序（得分高的在前）
        this.suggestFolderList.sort((a, b) => (b.score || 0) - (a.score || 0));
        
        // 限制拼音匹配结果数量（最多10个）
        this.suggestFolderList.slice(0, 10);
        
        if (!this.curInputText.startsWith('[网页]')) {
          for (let i = 0; i < this.searchHistoryList.length; i++) {
            const index = this.searchHistoryList[i].toLowerCase().indexOf(lowerInputText);
            if (index != -1) {
              this.suggestHistoryList.push({
                a: this.searchHistoryList[i].slice(0, index),
                b: this.searchHistoryList[i].slice(index, index + lowerInputText.length),
                c: this.searchHistoryList[i].slice(index + lowerInputText.length),
                text: this.searchHistoryList[i],
              });
            }
          }
          
          // 限制搜索历史结果数量（最多10个）
          this.suggestHistoryList.slice(0, 10);
        }
      }
    },

    /**
     * 将拼音匹配结果转换为搜索建议格式
     * @private
     * @param {Object} pinyinMatch - 拼音匹配结果
     * @param {Object} webFolderItem - 网页夹项目
     * @param {string} matchType - 匹配类型
     * @returns {Object} 搜索建议格式的结果
     */
    _convertPinyinMatchToSuggestion(pinyinMatch, webFolderItem, matchType) {
      // 简化的高亮处理：对于拼音匹配，高亮整个匹配的文本
      // 更精确的实现需要根据highlightRanges来处理
      let highlightStart = 0;
      let highlightEnd = pinyinMatch.matchedText.length;
      
      // 如果有高亮范围信息，使用第一个范围
      if (pinyinMatch.highlightRanges && pinyinMatch.highlightRanges.length > 0) {
        highlightStart = pinyinMatch.highlightRanges[0].start;
        highlightEnd = pinyinMatch.highlightRanges[0].end;
      }
      
      return {
        a: pinyinMatch.matchedText.slice(0, highlightStart),
        b: pinyinMatch.matchedText.slice(highlightStart, highlightEnd),
        c: pinyinMatch.matchedText.slice(highlightEnd),
        d: webFolderItem.src,
        text: pinyinMatch.matchedText,
        matchType: matchType,
        pinyinMatch: true,
        score: pinyinMatch.score
      };
    },

    /**
     * 触发一次云端（百度）搜索联想
     */
    triggerWebSearchSuggestion() {
      if (this.curInputText.startsWith('[网页]')) {
        this.suggestWebList = [];
        return;
      }
      if (this.curInputText.length < 2) {
        this.clearSearchSuggestion();
      } else if (this.suggestEnable && navigator.onLine) {
        let script = document.createElement('script');
        script.src = 'https://www.baidu.com/su?wd=' + encodeURIComponent(this.curInputText) + '&cb=searchCallBack';
        this.webSuggestExceedTimer = setTimeout(() => {
          this.webSuggestExceedTimer = null;
          script.remove();
        }, 10000);
        document.head.appendChild(script);
      }
    },

    /**
     * 初始化搜索联想，当输入框内容改变时，获得搜索建议
     * 输入框超过200ms未改变时进行本地检索匹配，超过600ms未改变时发起搜索建议请求
     */
    bindSearchSuggestion() {
      let input = document.getElementById('headMenuSearchInput');
      let enable = localStorage.getItem('suggestEnable');
      if (enable === null || enable === 'true') {
        this.suggestEnable = true;
        this.selectSuggestion(0);
      } else {
        this.suggestEnable = false;
      }
      input.addEventListener('input', () => {
        if (this.webSuggestDelayTimer != null) {
          clearTimeout(this.webSuggestDelayTimer);
          this.webSuggestDelayTimer = null;
        }
        if (this.localSuggestDelayTimer != null) {
          clearTimeout(this.localSuggestDelayTimer);
          this.localSuggestDelayTimer = null;
        }
        if (this.webSuggestExceedTimer != null) {
          clearTimeout(this.webSuggestExceedTimer);
          this.webSuggestExceedTimer = null;
          if (document.querySelector("script[src^='https://www.baidu.com/su?wd=']") != null)
            document.querySelector("script[src^='https://www.baidu.com/su?wd=']").remove();
        }
        
        this.curInputText = this.searchValue.trim();
        // 使用防抖函数进行搜索，避免频繁计算
        if (this.debouncedLocalSearch) {
          this.debouncedLocalSearch();
        } else {
          // 降级到原始实现
          this.localSuggestDelayTimer = setTimeout(() => {
            this.localSuggestDelayTimer = null;
            this.triggerLocalSearchSuggestion();
          }, 200);
        }
        
        if (this.debouncedWebSearch) {
          this.debouncedWebSearch();
        } else {
          // 降级到原始实现
          this.webSuggestDelayTimer = setTimeout(() => {
            this.webSuggestDelayTimer = null;
            this.triggerWebSearchSuggestion();
          }, 600);
        }
      });
    },

    /**
     * 切换搜索联想的启用状态
     * @param {*} text 按钮的文本
     */
    switchSuggest() {
      if (this.suggestEnable) {
        this.suggestEnable = false;
        localStorage.setItem('suggestEnable', false);
        document.getElementById('headMenuSearchInput').removeEventListener('input', () => { });
        this.suggestWebList = [];
      } else {
        localStorage.setItem('suggestEnable', true);
        this.suggestEnable = true;
        this.triggerWebSearchSuggestion();
      }
    },

    /**
     * 清空搜索联想列表
     */
    clearSearchSuggestion() {
      this.suggestFolderList = [];
      this.suggestWebList = [];
      this.suggestHistoryList = [];
    },

    /**
     * 更新搜索联想列表
     * @param {*} response 搜索联想的返回结果
     */
    searchCallBack(response) {
      clearTimeout(this.webSuggestExceedTimer);
      this.webSuggestExceedTimer = null;
      if (this.suggestEnable == false || this.searchValue == "") {
        return;
      }
      if (document.querySelector("script[src^='https://www.baidu.com/su?wd=']") != null)
        document.querySelector("script[src^='https://www.baidu.com/su?wd=']").remove();
      const matchWebList = response.s;
      let sett = new Set();
      for (let i = 0; i < this.suggestFolderList.length; i++) {
        sett.add(this.suggestFolderList[i].text);
      }
      for (let i = 0; i < this.suggestHistoryList.length; i++) {
        sett.add(this.suggestHistoryList[i].text);
      }
      this.suggestWebList = [];
      const webMaxNum = Math.max(0, 12 - this.suggestFolderList.length - this.suggestHistoryList.length); //最多显示12个搜索建议
      for (let i = 0; i < matchWebList.length; i++) {
        if (!sett.has(matchWebList[i])) {
          this.suggestWebList.push(matchWebList[i]);
        }
        if (this.suggestWebList.length >= webMaxNum) {
          break;
        }
      }
    },

    dragItem(e) {
      e.dataTransfer.setData('text/plain', e.target.getElementsByClassName('searchHistoryText')[0].innerText);
    },

    dropItemToSearchInput(e) {
      e.preventDefault();
      this.searchValue += e.dataTransfer.getData('text/plain');
    },

    /**
     * 预热拼音缓存
     */
    warmupPinyinCache() {
      if (typeof pinyinCache === 'undefined' || !this.webFolderText) {
        return;
      }

      try {
        // 提取所有网页夹名称用于预热
        const websiteNames = this.webFolderText.map(item => item.name);
        
        console.log('PinyinSearch: Starting cache warmup...');
        pinyinCache.warmup(websiteNames);
        const stats = pinyinCache.getStats();
        console.log('PinyinSearch: Cache warmup completed.', stats);
      } catch (error) {
        console.warn('PinyinSearch: Cache warmup failed:', error);
      }
    },
  },
  computed: {
    moneyTozhString() {
      //将超过4位的合成玉转换按右边的例子转换为中文，例如：12345->1.23万，123456->12.3万，1234567->123万，12345678->1234万，达到1亿的数字不再转换
      let money = parseInt(this.hcMoney);
      if (money < 10000) {
        return money;
      } else if (money < 100000) {
        return (money / 10000).toFixed(2) + '万';
      } else if (money < 1000000) {
        return (money / 10000).toFixed(1) + '万';
      } else if (money < 10000000) {
        return (money / 10000).toFixed(0) + '万';
      } else {
        return (money / 10000).toFixed(0) + '万';
      }
    },
  },
}).mount('#headMenu');
