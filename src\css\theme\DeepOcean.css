:root[theme="DeepOcean-day"],
:root[theme="DeepOcean-night"] {
  --Ga0: #f6f7f8;
  --Ga1: #f1f2f3;
  --Ga2: #e3e5e7;
  --Ga3: #c9ccd0;
  --Ga4: #aeb3b9;
  --Ga5: #9499a0;
  --Ga6: #797f87;
  --Ga7: #61666d;
  --Ga8: #484c53;
  --Ga9: #2f3238;
  --Ga10: #18191c;
  --Ga11: #ffffff;
  --Ga12: #f1f2f3;
  --Wh0: #ffffff;
  --Ba0: #000000;
  --Pi0: #fff3f6;
  --Pi1: #ffecf1;
  --Pi2: #ffd9e4;
  --Pi3: #ffb3ca;
  --Pi4: #ff8cb0;
  --Pi5: #ff6699;
  --Pi6: #e84b85;
  --Pi7: #d03171;
  --Pi8: #ad1c5b;
  --Pi9: #771141;
  --Pi10: #3f0723;
  --Ma0: #fef3fc;
  --Ma1: #fdebfa;
  --Ma2: #fbd7f4;
  --Ma3: #f7aeeb;
  --Ma4: #f286e2;
  --Ma5: #ee5ddb;
  --Ma6: #da41cb;
  --Ma7: #c525ba;
  --Ma8: #9b1797;
  --Ma9: #670f67;
  --Ma10: #330834;
  --Re0: #fef3f2;
  --Re1: #feecea;
  --Re2: #fdd7d4;
  --Re3: #fcafaa;
  --Re4: #fa857f;
  --Re5: #f85a54;
  --Re6: #e23d3d;
  --Re7: #c9272c;
  --Re8: #9f1922;
  --Re9: #710e18;
  --Re10: #3b060d;
  --Or0: #fff6ee;
  --Or1: #fff0e3;
  --Or2: #ffe1c7;
  --Or3: #ffc18f;
  --Or4: #ffa058;
  --Or5: #ff7f24;
  --Or6: #e95b03;
  --Or7: #bb4100;
  --Or8: #8d2d00;
  --Or9: #5e1b00;
  --Or10: #2f0c00;
  --Ye0: #fffaef;
  --Ye1: #fff6e4;
  --Ye2: #ffeec9;
  --Ye3: #ffdb93;
  --Ye4: #ffc65d;
  --Ye5: #ffb027;
  --Ye6: #e58900;
  --Ye7: #b76800;
  --Ye8: #8a4a00;
  --Ye9: #5b2e00;
  --Ye10: #2f1600;
  --Ly0: #fffcec;
  --Ly1: #fffadf;
  --Ly2: #fff5bf;
  --Ly3: #ffea80;
  --Ly4: #ffdc40;
  --Ly5: #ffcc00;
  --Ly6: #d5a300;
  --Ly7: #aa7d00;
  --Ly8: #805a00;
  --Ly9: #553900;
  --Ly10: #2b1b00;
  --Lg0: #f7fbef;
  --Lg1: #f2f9e4;
  --Lg2: #e3f2c8;
  --Lg3: #c7e691;
  --Lg4: #a9d95b;
  --Lg5: #88cc24;
  --Lg6: #66b105;
  --Lg7: #4e8e04;
  --Lg8: #376a03;
  --Lg9: #224702;
  --Lg10: #102301;
  --Gr0: #effbf3;
  --Gr1: #e4f8ea;
  --Gr2: #caf1d6;
  --Gr3: #95e4af;
  --Gr4: #5fd689;
  --Gr5: #2ac864;
  --Gr6: #0eb350;
  --Gr7: #089043;
  --Gr8: #046e35;
  --Gr9: #034926;
  --Gr10: #012414;
  --Cy0: #edfbfb;
  --Cy1: #e2f8f8;
  --Cy2: #c4eff0;
  --Cy3: #89e1e1;
  --Cy4: #4fd3d1;
  --Cy5: #14c4bf;
  --Cy6: #02aaaa;
  --Cy7: #018488;
  --Cy8: #015f66;
  --Cy9: #013d44;
  --Cy10: #001d22;
  --Lb0: #ecfafe;
  --Lb1: #dff6fd;
  --Lb2: #bfedfa;
  --Lb3: #80daf6;
  --Lb4: #40c5f1;
  --Lb5: #00aeec;
  --Lb6: #008ac5;
  --Lb7: #00699d;
  --Lb8: #004b76;
  --Lb9: #002f4f;
  --Lb10: #001627;
  --Bl0: #f3f5ff;
  --Bl1: #ebefff;
  --Bl2: #d7dfff;
  --Bl3: #b0c1ff;
  --Bl4: #88a4ff;
  --Bl5: #6188ff;
  --Bl6: #4c6de4;
  --Bl7: #3752c8;
  --Bl8: #2136ac;
  --Bl9: #121f7f;
  --Bl10: #080d41;
  --Pu0: #f9f4ff;
  --Pu1: #f6edff;
  --Pu2: #eddbff;
  --Pu3: #d8b6ff;
  --Pu4: #c392ff;
  --Pu5: #ac6dff;
  --Pu6: #8f56e4;
  --Pu7: #723ecc;
  --Pu8: #5627b3;
  --Pu9: #371683;
  --Pu10: #190a44;
  --Br0: #faf8f6;
  --Br1: #f7f3f0;
  --Br2: #efe7e0;
  --Br3: #e0cfc1;
  --Br4: #d0b7a3;
  --Br5: #c19d84;
  --Br6: #a5816a;
  --Br7: #856553;
  --Br8: #634a3e;
  --Br9: #423029;
  --Br10: #211815;
  --Si0: #f9fbfc;
  --Si1: #f5f7fa;
  --Si2: #ebeff4;
  --Si3: #d7e0ea;
  --Si4: #c3d0df;
  --Si5: #afc0d5;
  --Si6: #8d9fb9;
  --Si7: #6d7f9c;
  --Si8: #4d5d7c;
  --Si9: #323d54;
  --Si10: #191e2b;
}

/* 我的空间 主题配色: DeepOcean*/
:root[theme="DeepOcean-day"],
:root[theme="DeepOcean-night"] {
  --themeColor: #95d1ff;
  --themeColor2: var(var(--Lb8));
  --themeBack: rgb(44, 48, 49);
  --backC1: rgb(25, 25, 25);

  color: #d5d5d5;
  --title-color: #d5d5d5;
  --title-color2: #d5d5d5;
  --windowShadowColor: #b0b0b066;
  --defaultColor: #d5d5d5;
  --searchChColor: #dfdfdf;
  --back: linear-gradient(
    192deg,
    var(--Lb8),
    var(--Lb9) 10%,
    var(--backC1) 50%
  );
  --back2: linear-gradient(
    192deg,
    var(--Lb8),
    var(--Lb9) 10%,
    var(--backC1) 50%
  );
  --back3: url("/images/background/background4.png") calc(8.5vw + 550px) -66px no-repeat;
  --bgSize1: 100%;
  --bgSize2: 100%;
  --bgSize3: 57%;
  --theme: url("/images/snowflake.svg") no-repeat center 200px;
  --themeSize: 40%;
  --blockBackC: var(--themeBack);
  --rightblockBack: var(--themeBack);
  --blockBackC2: rgb(33, 33, 33);

  --headMenuBack: linear-gradient(
    270deg,
    rgb(0, 0, 0, 0.08),
    rgb(0, 0, 0, 0.15)
  );
  --headMenuBack2: linear-gradient(
    192deg,
    var(--Lb6),
    var(--Lb7) 4%,
    var(--Lb8) 15%,
    var(--Lb9) 70%,
    var(--Lb10) 100%
  );
  --headMenuColor: white;
  --headMenuColor2: var(--themeColor);

  --inputBack: rgb(40, 44, 45);
  --searchChBackC: rgb(53, 53, 53);
  --dropMenuBackC: rgb(53, 53, 53);
  --dropMenuBackC2: rgb(60, 66, 66);
  --headInputColor: var(--Ga5);
  --headInputColor2: white;
  --headInputBack: transparent;
  --headTitleBack: linear-gradient(to bottom, rgb(53, 58, 53), rgb(45, 48, 48));
  --btnBackC: rgb(60, 66, 66);
  --btnBackC2: transparent;
  --btnBackC3: var(--Lb8);
  /* --btnHoverColor: #eeeeee; */
  --btnHoverShadow: transparent;
  /* --dropMenuHoverColor:var(--themeBack2); */
  --btnBor: 2px solid rgb(145, 150, 145);
  --btnBor2: 2px solid var(--themeColor);
  --ibtnColor: rgba(160, 160, 160, 0.8);
  --ibtnHoverColor: var(--themeColor);
  --divBack: linear-gradient(140deg, #212326a0 40%, #002f4f30);
  --divEditBack: #323232;
  --noteBor: 2px solid var(--Ga8);
  --noteBor2: 2px solid var(--Lb6);
  --signBor: 2px solid var(--Ga8);
  --signBor2: 2px solid var(--Lb6);
  --footBack: linear-gradient(var(--backC1), #101212);
  --footColor: rgb(251, 246, 246);

  --line: 1px solid #414142;
  --line2: 1px solid #808384;
  --line3: 1px solid #687273;
  --horizontal-divider-color: rgba(234, 207, 209, 0.7);
  --table-border-color: 0.1em solid rgb(110, 110, 110);
  --hnColor1: #aaaaaa;
  --hnColor2: #eeeeee;

  --seaItemBack: rgb(0, 0, 0, 0.1);
  --seaItemBack2: rgb(80, 80, 80, 0.2);
  --seaItemBor: 1px solid var(--Ga6);
  --seaItemBor2: 1px solid var(--Lb5);
  --seaItemShadow: 0px 0px 5px var(--Lb5);
  --seaItemColor: var(--defaultColor);
  --seaItemColor2: var(--themeColor);
  --seaItemIconColor: var(--Ga2);
  --bodyScrollBack: linear-gradient(
    192deg,
    var(--Lb6),
    var(--Lb7) 1.7%,
    var(--Lb8) 4.4%,
    var(--Lb9) 15%,
    var(--backC1) 75%
  );
  --scrollt1: rgba(220, 220, 220, 0.15);
  --scrollt2: rgba(220, 220, 220, 0.35);
  --scrollt3: rgba(220, 220, 220, 0.45);
  --scrolltshadow: rgba(220, 220, 220, 0.6);
}
