# 网页开发日志

版本: 2.6

---

### 版本更新[2.6] 2025/08/20

- 搜索模块的网页夹检索支持拼音输入匹配；待办模块新增图片智能粘贴功能。
- 调整搜索候选列表；扩大脚本搜索支持的网页范围。
- 调整Windows系统更新后网页的部分字体效果；优化细节。

### 版本更新[2.5] 2025/03/03

- 待办模块更名为笔记模块，新增隐藏笔记功能，在日志中存储笔记历史；新增脚本搜索功能，目前支持豆包、CHAT ECNU；强化“签退”的作用，包括关闭搜索模块，减少后端的工作任务。
- 优化中文资讯的爬取机制，提高成功率，同时新增一个中文资讯来源[智源社区](https://hub.baai.ac.cn/)；将气温模块的预测图从5日增加到10日。
- 美化网页夹网页布局；调整搜索源和跳转链接；优化细节，修复bug，提高加载速度。

### 版本更新[2.4c] 2025/01/02

- 正式落地多端同步功能；优化部分细节，更新爬虫机制。

### 版本更新[2.4b] 2024/09/27

- 发布多端同步beta版；调整系统结构，提高代码可移植性，适配多端访问；不再使用本地MongoDB存储数据。
- 新增一个中文资讯来源[机器之心](https://www.jiqizhixin.com/)；待办模块不再支持mermaid语法。

### 版本更新[2.4] 2024/09/08

- 新增中文资讯模块（原百度热搜模块，现包含多平台资讯，数据来源[今日热榜](https://tophub.today)）；新增国际新闻模块自动刷新功能。
- 优化网页夹图标爬取机制（使用puppeteer提高爬虫成功率，增加一定耗时）；调整导航栏选项和搜索引擎候选列表；优化五日气温曲线图；修复2.3版本bug。

### 版本更新[2.3] 2024/06/16

- 删除个人空间。
- 调整签到签退的奖池机制及其用途，并新增相关功能；主题“幻梦”新增夜间模式。
- 优化搜索框的交互机制；优化待办编辑文本的取消选中文本行为；优化细节，增加鼠标交互动感。

### 版本更新[2.2] 2024/03/10

- 优化后台网络信息爬取机制，减少部分场景的卡顿和错误；正式落地气象模块；新增搜索历史联想功能，搜索历史不再包含网页。
- 调整百度热搜模块位置；调整日历模块显示方式；调整待办删除方式；删除主题配色“生命流”和“孤星”。
- 优化搜索下拉框显示效果；优化主题配色“现代”，更名为“幻梦”(FantasyDream)；优化细节，调整动画曲线，提升用户体验。

### 版本更新[2.1] 2023/12/28

- 新增气象模块beta版，气象数据来自[百度天气](https://weathernew.pae.baidu.com/weathernew/pc?query=%E6%B5%99%E6%B1%9F%E6%9D%AD%E5%B7%9E%E5%A4%A9%E6%B0%94&srcid=4982)；搜索模块新增网页夹联想跳转功能；删除名言警句模块、个人信息模块。
- 调整网页夹管理页面的布局；调整日历模块的显示效果。
- 新增主题配色“现代”(Modern)；优化Markdown语法的代码显示效果（部分借鉴于[文章](https://qa.1r1g.com/sf/ask/2891475821/)）；优化主页交互的一些细节。

### 版本更新[2.0] 2023/10/13

- 迭代部分早期开发模块的代码，使用Mongo数据库存储数据，全面调整数据存储方式，分离代码与数据。
- 新增arknights采购中心模块；完善arknights干员一览模块；新增网页夹模块以取代网页快捷跳转模块，增强可维护性和实用性。
- 删除日历模块日程记录功能。作为功能上的替代，待办模块支持Markdown中mermaid语法的渲染。
- 新增主题配色“秋庭”(AutumnCourt)；提高部分动画渲染帧率；优化部分文本的字体样式和Markdown语法的显示效果。

### 版本更新[1.2] 2023/07/16

- 待办模块支持Markdown语法，现可在待办中插入图片、表格，生成html元素等；搜索模块新增搜索联想功能，联想建议来自百度搜索。
- 删除ChatGPT对话模块beta版。
- 新增主题配色“孤星”(LoneTrail)；美化界面展示，包括隐藏主界面滚动条、待办模块按钮图标化、调整部分字体和间距等。

### 版本更新[1.1] 2023/04/24

- 新增“WORLD NEWS”模块，该模块用英文展示部分国际新闻，数据来源于[Browserling](https://sciurls.com/)；新增ChatGPT对话模块beta版。
- 待办项目支持tab键输入；优化数据存储方式以保持多浏览器的信息同步。
- 优化细节，修复Bug。

### 版本更新[1.0] 2023/02/06

- 本网站后端现在可以通过nssm封装为windows服务实现开机自启动。
- 个人空间初步落地arknights角色系统的部分模块；新增网页主题配色切换功能，推出主题配色“深海”(DeepOcean)、“生命流”(LifeFlow)。
- 优化搜索模块外观，新增删除单条搜索历史记录功能；优化热搜模块外观，现在可以隐藏热搜，点击标题可以刷新热搜。
- 完善签退功能，现在签退后将关闭百度热搜和网页跳转模块，直至次日完成签到；调整待办模板，现在新增待办时有首行标题，且粘贴文本时现在自动过滤富文本格式。
- 鉴于浏览器的机制和应用场景，删除“专注画廊”模块；调整导航栏机制，优化布局样式，适配2K等分辨率；热搜模块修复特殊字符未转义BUG。

### 版本更新[0.4] 2022/10/26

- 个人空间初步完成功能选择界面开发。
- 新增“专注画廊”模块；新增百度热搜模块；新增签到模块及子功能“心跳幸运墙”。
- 调整待办模块的交互机制，由于在文本编辑状态下拖拽待办项目体验较差，因此在单击“管理”按键后还需双击项目方可编辑；删除倒计时闹钟模块；删除搜索模块的网址识别功能。
- 优化网页跳转模块和待办模块，在内容过多的情况下，通过滚动条进行交互；优化搜索历史记录模块，当前历史记录被再次搜索后会上移；搜索网站选项增加知乎选项。
- 优化导航栏、底部栏的布局和部分字体样式；优化了日志页面的滚动条样式；优化“心跳幸运墙”功能布局样式。

### 版本更新[0.3] 2022/08/13

- 将本网站部署到了127.0.0.1上，初步实现前后端的通信架构雏形。通过pkg将后端打包为exe，让exe文件开机自启动。
- 调整待办模块底层机制，调用第三方库实现拖拽功能，提高便捷性；日历模块中的节假日实现前端存储，与待办一样存储在浏览器中；
- 搜索网站选项增加了选项Github和CSDN；新增了搜索模块“搜索历史记录”子功能；优化待办模块的文本存储，实现多行存储和自主换行。
- 全面更新主页的背景与配色，适配主页在一些屏幕尺寸下的表现效果；对主页的部分元素布局进行优化；对搜索框的特殊字符输入实现编码处理；基于安全性和功能性考量，将待办文字的元素从label改为input。

### 版本更新[0.2] 2022/06/18

- 新增待办项目的置顶功能；新增简易版的倒计时闹种模块。
- 临近期末，临时新增了一个复习时间表的模块，计划了最后半个月的复习安排。
- 优化日历模块中字体颜色优先级；名言警句模块从每日循环更新修改为了刷新就更新，更新规则改为随机选择。
- 优化网页布局和导航栏的部分效果；优化了一些配色和布局等，使用文本内部滚动条来取代外部滚动条；

### 网站创建[0.1] 2022/03/31

- 实现网站基本功能，包括日历，待办，外接了搜索引擎的搜索功能。待办的内容存储在浏览器中，将日历的内容存储在html中。本网站目前仅针对火狐适配。
- 现在本网站的前期开发工作已经结束，接下来将进入网站的长期维护和更新阶段，因此设计了一个版本更新日志。
