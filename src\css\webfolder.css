body {
  min-width: 1080px;
  overflow: hidden;
  font-family: "Arial";
}

#mainWindow {
  position: absolute;
  background-color: var(--blockBackC);
  top: 65px;
  left: 5%;
  width: calc(92% - 80px);
  border-radius: 14px;
  padding: 15px 25px;
  z-index: 20;
  opacity: 0.88;
  height: calc(100% - 116px);
  box-shadow: 2px 2px 10px var(--windowShadowColor);
}

#backImage {
  z-index: 0;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  background: var(--back2);
  height: 100%;
  background-size: var(--bgSize2);
}

#webFolderDis {
  width: 100%;
  height: calc(100% - 50px);
  margin-bottom:8px;
}

#webFolderTitle {
  width: 100%;
  display: flex;
  overflow: hidden;
}

#webFolderBody {
  max-height: calc(100% - 46px);
  margin-top: 2px;
  overflow: scroll;
  scrollbar-width: none; /* 隐藏滚动条 */
  -ms-overflow-style: none; /* 隐藏滚动条 */
}

#webFolderBody::-webkit-scrollbar {
  display: none; /* 隐藏滚动条 */
}

#webFolderTitle span {
  height: 38px;
  line-height: 38px;
  font-size: 17px;
  text-align: center;
  flex: 1;
  border: 1px solid #888888;
  border-right: 1px transparent;
  font-weight: bold;
}

.webFolderRow > span {
  line-height: 33px;
  height: 33px;
  text-align: center;
  border: 1px solid #888888;
  border-right: 1px transparent;
  font-style: italic;
}

.webFolderRow:hover{
  box-shadow: 0px 0px 3px var(--themeColor);
}

.webFolderRow:hover span{
  border-top: 1px solid var(--themeColor)!important;
  border-bottom: 1px solid var(--themeColor)!important;
}

.webFolderRow {
  display: flex;
  margin: 4px 0;
}

#webFolderTitle > span:nth-child(1),
.webFolderRow > span:nth-child(1) {
  flex: 17;
}

#webFolderTitle > span:nth-child(2),
.webFolderRow > span:nth-child(2) {
  flex: 11;
}

#webFolderTitle > span:nth-child(3),
.webFolderRow > span:nth-child(3) {
  flex: 28;
}
#webFolderTitle > span:nth-child(4),
.webFolderRow > span:nth-child(4) {
  flex: 25;
}

#webFolderTitle > span:nth-child(5),
.webFolderRow > span:nth-child(5) {
  flex: 6;
}

#webFolderTitle > span:nth-child(6),
.webFolderRow > span:nth-child(6) {
  flex: 10;
  border-right: 1px solid #888888;
}
.webFolderRow input {
  width: calc(100% - 20px);
  background-color: transparent;
  border: none;
  color: inherit;
  font-size: 16px;
  padding: 0;
  margin: 0 10px;
  height: 100%;
}

.opButton {
  cursor: pointer;
  margin: 0 10px;
  font-size: 20px;
  /* line-height: 35px; */
}

.opButton:hover {
  color: var(--themeColor);
}

.redText {
  color: red !important;
}

.redText2 {
  color: red !important;
}

.webFolderBtn {
  color: var(--defaultColor);
  background-color: var(--btnBackC);
  border: var(--btnBor);
  height: 44px;
  border-radius: 12px;
  line-height: 44px;
  cursor: pointer;
  display: inline-block;
  font-size: 16px;
  width: 220px;
  text-align: center;
  -webkit-transition: all 0.15s ease-in-out;
  -moz-transition: all 0.15s ease-in-out;
  -ms-transition: all 0.15s ease-in-out;
  -o-transition: all 0.15s ease-in-out;
  transition: all 0.15s ease-in-out;
}

.webFolderBtn:hover {
  background-color: var(--btnBackC3);
  border: var(--btnBor2);
  color: var(--btnHoverColor) !important;
}

#loadWin {
  position: fixed;
  backdrop-filter: blur(3px);
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 180px;
  height: 180px;
  background: rgba(30, 30, 30, 0.15);
  border: 3px solid #3c3c3c;
  border-radius: 50%;
  text-align: center;
  line-height: 180px;
  font-size: 20px;
  color: rgb(255, 240, 0);
  letter-spacing: 5px;
  text-transform: uppercase;
  text-shadow: 0 0 10px rgb(255, 240, 0);
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
  z-index: 95;
}

.loadWin1::before {
  content: "";
  position: absolute;
  top: -2px;
  left: -2px;
  width: 100%;
  height: 100%;
  border: 4px solid transparent;
  border-top: 4px solid rgb(255, 240, 0);
  border-right: 4px solid rgb(255, 240, 0);
  border-radius: 50%;
  animation: animateCircle 2s linear infinite;
}

#loadBall {
  display: block;
  position: absolute;
  top: calc(50% - 0px);
  left: 50%;
  width: 50%;
  height: 4px;
  background: transparent;
  transform-origin: left;
  /* animation: animate 2s linear infinite; */
  z-index: 10;
}

#loadBall::before {
  content: "";
  position: absolute;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background-color: rgb(255, 240, 0);
  top: -8px;
  right: -8px;
  box-shadow: 0 0 20px rgb(255, 240, 0);
}

#BgAnima {
  position: absolute;
  height: 100%;
  width: 100%;
  left: 0px;
  top: 0px;
  font-size: 18px;
  z-index: 5;
  display: block;
}

@keyframes animate {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

@keyframes animateCircle {
  0% {
    transform: rotate(-44deg);
  }

  100% {
    transform: rotate(316deg);
  }
}

#BgMask {
  background: rgba(0, 0, 0, 0.45);
  /* background: url("../images/bg_0_babel.png") center center; */
  z-index: 90;
  display: block;
  position: fixed;

  height: 100%;
  width: 100%;
  left: 0px;
  top: 0px;
}
