/**
 * 媒体上传处理器
 * 处理媒体文件上传到后端，管理上传状态，防止并发上传
 */
class MediaUploadHandler {
  /**
   * 构造函数
   */
  constructor() {
    // 上传状态管理
    this.isUploading = false;
    this.uploadQueue = [];
    
    // 配置
    this.config = {
      uploadUrl: '/api/upload-media',
      maxFileSize: 10 * 1024 * 1024, // 10MB
      timeout: 30000, // 30秒超时
      supportedTypes: [
        'image/png',
        'image/jpeg', 
        'image/jpg',
        'image/gif',
        'image/webp'
      ]
    };
    
    console.log('MediaUploadHandler: Initialized successfully');
  }

  /**
   * 上传媒体文件
   * @param {File} file - 要上传的文件
   * @param {string} mediaType - 媒体类型 (MIME type)
   * @returns {Promise<Object>} 上传结果
   */
  async uploadMedia(file, mediaType) {
    // 检查是否正在上传 (需求 3.5: 前端一次只能传输一个文件)
    if (this.isUploading) {
      console.log('MediaUploadHandler: Upload in progress, ignoring new upload request');
      return {
        success: false,
        error: '正在上传中，请稍后再试',
        code: 'UPLOAD_IN_PROGRESS'
      };
    }

    // 验证文件
    const validation = this._validateFile(file, mediaType);
    if (!validation.valid) {
      console.error('MediaUploadHandler: File validation failed:', validation.error);
      return {
        success: false,
        error: validation.error,
        code: validation.code
      };
    }

    try {
      // 设置上传状态
      this.isUploading = true;
      console.log(`MediaUploadHandler: Starting upload - Type: ${mediaType}, Size: ${file.size} bytes`);

      // 创建FormData
      const formData = new FormData();
      formData.append('file', file);
      formData.append('mediaType', mediaType);

      // 执行上传
      const result = await this._performUpload(formData);
      
      console.log('MediaUploadHandler: Upload completed successfully:', result);
      return {
        success: true,
        filename: result.filename,
        mediaType: result.mediaType,
        size: result.size
      };

    } catch (error) {
      console.error('MediaUploadHandler: Upload failed:', error);
      return {
        success: false,
        error: this._getErrorMessage(error),
        code: error.code || 'UPLOAD_FAILED'
      };
    } finally {
      // 重置上传状态
      this.isUploading = false;
    }
  }

  /**
   * 执行文件上传
   * @private
   * @param {FormData} formData - 表单数据
   * @returns {Promise<Object>} 服务器响应
   */
  _performUpload(formData) {
    return new Promise((resolve, reject) => {
      const xhr = new XMLHttpRequest();
      
      // 设置超时
      xhr.timeout = this.config.timeout;
      
      // 处理响应
      xhr.onload = () => {
        try {
          if (xhr.status === 200) {
            const response = JSON.parse(xhr.responseText);
            resolve(response);
          } else {
            const errorResponse = xhr.responseText ? JSON.parse(xhr.responseText) : {};
            reject({
              status: xhr.status,
              message: errorResponse.error || `HTTP ${xhr.status}`,
              code: errorResponse.code || 'HTTP_ERROR'
            });
          }
        } catch (parseError) {
          reject({
            status: xhr.status,
            message: '服务器响应格式错误',
            code: 'PARSE_ERROR'
          });
        }
      };

      // 处理网络错误
      xhr.onerror = () => {
        reject({
          status: 0,
          message: '网络连接失败',
          code: 'NETWORK_ERROR'
        });
      };

      // 处理超时
      xhr.ontimeout = () => {
        reject({
          status: 0,
          message: '上传超时',
          code: 'TIMEOUT_ERROR'
        });
      };

      // 处理中断
      xhr.onabort = () => {
        reject({
          status: 0,
          message: '上传被中断',
          code: 'ABORT_ERROR'
        });
      };

      // 发送请求
      xhr.open('POST', this.config.uploadUrl, true);
      xhr.send(formData);
    });
  }

  /**
   * 验证文件
   * @private
   * @param {File} file - 要验证的文件
   * @param {string} mediaType - 媒体类型
   * @returns {Object} 验证结果
   */
  _validateFile(file, mediaType) {
    // 检查文件是否存在
    if (!file) {
      return {
        valid: false,
        error: '文件不存在',
        code: 'FILE_NOT_FOUND'
      };
    }

    // 检查文件大小
    if (file.size > this.config.maxFileSize) {
      const maxSizeMB = Math.round(this.config.maxFileSize / (1024 * 1024));
      return {
        valid: false,
        error: `文件大小超过限制 (最大 ${maxSizeMB}MB)`,
        code: 'FILE_TOO_LARGE'
      };
    }

    // 检查文件类型
    if (!this.config.supportedTypes.includes(mediaType)) {
      return {
        valid: false,
        error: `不支持的文件格式: ${mediaType}`,
        code: 'UNSUPPORTED_TYPE'
      };
    }

    return {
      valid: true
    };
  }

  /**
   * 获取错误消息
   * @private
   * @param {Object} error - 错误对象
   * @returns {string} 用户友好的错误消息
   */
  _getErrorMessage(error) {
    const errorMessages = {
      'NETWORK_ERROR': '网络连接失败，请检查网络连接',
      'TIMEOUT_ERROR': '上传超时，请重试',
      'ABORT_ERROR': '上传被中断',
      'PARSE_ERROR': '服务器响应格式错误',
      'HTTP_ERROR': '服务器错误',
      'FILE_NOT_FOUND': '文件不存在',
      'FILE_TOO_LARGE': '文件过大',
      'UNSUPPORTED_TYPE': '不支持的文件格式'
    };

    return errorMessages[error.code] || error.message || '上传失败，请重试';
  }

  /**
   * 检查是否正在上传
   * @returns {boolean} 是否正在上传
   */
  isUploadInProgress() {
    return this.isUploading;
  }

  /**
   * 获取支持的文件类型
   * @returns {string[]} 支持的MIME类型数组
   */
  getSupportedTypes() {
    return [...this.config.supportedTypes]; // 返回副本以防止外部修改
  }

  /**
   * 更新配置
   * @param {Object} newConfig - 新的配置选项
   */
  updateConfig(newConfig) {
    this.config = { ...this.config, ...newConfig };
    console.log('MediaUploadHandler: Configuration updated:', this.config);
  }

  /**
   * 销毁处理器，清理资源
   */
  destroy() {
    // 如果正在上传，标记为中断
    if (this.isUploading) {
      console.log('MediaUploadHandler: Destroying while upload in progress');
      this.isUploading = false;
    }
    
    // 清空队列
    this.uploadQueue = [];
    
    console.log('MediaUploadHandler: Destroyed successfully');
  }
}