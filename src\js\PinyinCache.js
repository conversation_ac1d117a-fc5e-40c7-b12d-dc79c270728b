/**
 * 拼音缓存管理器
 * 提供内存缓存功能，存储拼音转换结果以提高性能
 */
class PinyinCache {
  constructor() {
    // 内存缓存存储拼音转换结果
    this.cache = new Map();
    
    // 缓存统计信息
    this.stats = {
      hits: 0,
      misses: 0,
      totalRequests: 0
    };
    
    // 缓存配置
    this.config = {
      maxSize: 1000,        // 最大缓存条目数
      maxAge: 30 * 60 * 1000, // 缓存过期时间（30分钟）
      cleanupInterval: 5 * 60 * 1000 // 清理间隔（5分钟）
    };
    
    // 启动定期清理
    this.startCleanupTimer();
  }

  /**
   * 生成缓存键
   * @private
   * @param {string} text - 原始文本
   * @param {string} type - 转换类型 ('full', 'array')
   * @returns {string} 缓存键
   */
  _generateKey(text, type) {
    return `${type}:${text}`;
  }

  /**
   * 获取缓存项
   * @param {string} text - 原始文本
   * @param {string} type - 转换类型
   * @returns {any|null} 缓存的结果，如果不存在或已过期则返回null
   */
  get(text, type) {
    this.stats.totalRequests++;
    
    const key = this._generateKey(text, type);
    const cacheItem = this.cache.get(key);
    
    if (!cacheItem) {
      this.stats.misses++;
      return null;
    }
    
    // 检查是否过期
    if (Date.now() - cacheItem.timestamp > this.config.maxAge) {
      this.cache.delete(key);
      this.stats.misses++;
      return null;
    }
    
    // 更新访问时间
    cacheItem.lastAccess = Date.now();
    this.stats.hits++;
    
    return cacheItem.value;
  }

  /**
   * 设置缓存项
   * @param {string} text - 原始文本
   * @param {string} type - 转换类型
   * @param {any} value - 要缓存的值
   */
  set(text, type, value) {
    const key = this._generateKey(text, type);
    const now = Date.now();
    
    // 如果缓存已满，删除最旧的项目
    if (this.cache.size >= this.config.maxSize) {
      this._evictOldest();
    }
    
    this.cache.set(key, {
      value: value,
      timestamp: now,
      lastAccess: now
    });
  }

  /**
   * 删除最旧的缓存项
   * @private
   */
  _evictOldest() {
    let oldestKey = null;
    let oldestTime = Date.now();
    
    for (const [key, item] of this.cache.entries()) {
      if (item.lastAccess < oldestTime) {
        oldestTime = item.lastAccess;
        oldestKey = key;
      }
    }
    
    if (oldestKey) {
      this.cache.delete(oldestKey);
    }
  }

  /**
   * 清理过期的缓存项
   * @private
   */
  _cleanup() {
    const now = Date.now();
    const keysToDelete = [];
    
    for (const [key, item] of this.cache.entries()) {
      if (now - item.timestamp > this.config.maxAge) {
        keysToDelete.push(key);
      }
    }
    
    keysToDelete.forEach(key => this.cache.delete(key));
    
    if (keysToDelete.length > 0) {
      console.log(`PinyinCache: Cleaned up ${keysToDelete.length} expired items`);
    }
  }

  /**
   * 启动定期清理定时器
   * @private
   */
  startCleanupTimer() {
    setInterval(() => {
      this._cleanup();
    }, this.config.cleanupInterval);
  }

  /**
   * 清空所有缓存
   */
  clear() {
    this.cache.clear();
    this.stats = {
      hits: 0,
      misses: 0,
      totalRequests: 0
    };
  }

  /**
   * 获取缓存统计信息
   * @returns {Object} 统计信息对象
   */
  getStats() {
    const hitRate = this.stats.totalRequests > 0 
      ? (this.stats.hits / this.stats.totalRequests * 100).toFixed(2)
      : '0.00';
      
    return {
      ...this.stats,
      hitRate: `${hitRate}%`,
      cacheSize: this.cache.size,
      maxSize: this.config.maxSize
    };
  }

  /**
   * 预热缓存 - 为常用文本预先生成缓存
   * @param {Array<string>} texts - 要预热的文本数组
   */
  warmup(texts) {
    if (!Array.isArray(texts)) {
      return;
    }
    
    console.log(`PinyinCache: Starting warmup for ${texts.length} items`);
    
    texts.forEach(text => {
      if (typeof text === 'string' && text.length > 0) {
        // 预热全拼、首字母和数组格式
        try {
          if (!this.get(text, 'full')) {
            const fullPinyin = PinyinConverter.getFullPinyin(text);
          }
        } catch (error) {
          console.warn(`PinyinCache: Warmup failed for "${text}":`, error);
        }
      }
    });
    
    console.log(`PinyinCache: Warmup completed. Cache size: ${this.cache.size}`);
  }
}

// 创建全局缓存实例
const pinyinCache = new PinyinCache();

// 如果在Node.js环境中，导出模块
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { PinyinCache, pinyinCache };
}