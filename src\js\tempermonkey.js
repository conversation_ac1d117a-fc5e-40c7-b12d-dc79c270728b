// ==UserScript==
// @name         homeHTML插件
// @namespace    http://tampermonkey.net/
// @version      0.2.1
// @description  私人使用
// <AUTHOR>
// @match        http://127.0.0.1:6699/*
// @match        https://www.doubao.com/chat/
// @match        https://chat.ecnu.edu.cn/html/
// @match        https://sso.ecnu.edu.cn/*
// @match        https://chat.deepseek.com/
// @grant        GM_setValue
// @grant        GM_getValue
// @grant        GM_deleteValue
// ==/UserScript==

(function () {
  'use strict';

  class TextAreaSimulator {
    constructor(element, interval = 90) {
      this.element = element;
      this.interval = interval;
    }

    simulateKeyEvents(char) {
      const keyCode = char === 'Enter' ? 13 : char.charCodeAt(0);

      const events = [
        new KeyboardEvent('keydown', {
          key: char,
          code: `Key${char}`,
          keyCode,
          which: keyCode,
          bubbles: true,
        }),
        new KeyboardEvent('keypress', {
          key: char,
          code: `Key${char}`,
          keyCode,
          which: keyCode,
          bubbles: true,
        }),
      ];

      events.forEach(evt => this.element.dispatchEvent(evt));

      if (char !== 'Enter') {
        const nativeSetter = Object.getOwnPropertyDescriptor(
          window.HTMLTextAreaElement.prototype,
          'value'
        ).set;
        nativeSetter.call(this.element, this.element.value + char);
      }

      const inputEvt = new Event('input', { bubbles: true });
      this.element.dispatchEvent(inputEvt);

      const keyupEvt = new KeyboardEvent('keyup', {
        key: char,
        code: `Key${char}`,
        keyCode,
        which: keyCode,
        bubbles: true,
      });
      this.element.dispatchEvent(keyupEvt);
    }

    type(text, index = 0) {
      if (index >= text.length) {
        return;
      }
      this.element.focus();
      const char = text[index];
      let randomInterval = this.interval + (Math.random() * 40 - 20);

      if (char === '\n') {
        randomInterval = this.interval * 2.5;
      }

      setTimeout(() => {
        if (char === '\n') {
          this.simulateKeyEvents('Enter');
        } else {
          this.simulateKeyEvents(char);
        }

        this.type(text, index + 1);
      }, randomInterval);
    }
  }

  class InputSimulator {
    constructor(element, interval = 90) {
      this.element = element;
      this.interval = interval;
    }

    simulateKeyEvents(char) {
      const keyCode = char.charCodeAt(0);

      const events = [
        new KeyboardEvent('keydown', {
          key: char,
          code: `Key${char}`,
          keyCode,
          which: keyCode,
          bubbles: true,
        }),
        new KeyboardEvent('keypress', {
          key: char,
          code: `Key${char}`,
          keyCode,
          which: keyCode,
          bubbles: true,
        }),
      ];

      events.forEach(evt => this.element.dispatchEvent(evt));

      const nativeSetter = Object.getOwnPropertyDescriptor(
        window.HTMLInputElement.prototype,
        'value'
      ).set;
      nativeSetter.call(this.element, this.element.value + char);

      const inputEvt = new Event('input', { bubbles: true });
      this.element.dispatchEvent(inputEvt);

      const keyupEvt = new KeyboardEvent('keyup', {
        key: char,
        code: `Key${char}`,
        keyCode,
        which: keyCode,
        bubbles: true,
      });
      this.element.dispatchEvent(keyupEvt);
    }

    type(text, index = 0) {
      if (index >= text.length) {
        return;
      }
      this.element.focus();
      const char = text[index];
      let randomInterval = this.interval + (Math.random() * 40 - 20);

      setTimeout(() => {
        this.simulateKeyEvents(char);
        this.type(text, index + 1);
      }, randomInterval);
    }
  }

  function scriptSearch(keyword, inputElement) {
    const inputText = inputElement.value; // 获取输入框中的文本
    if (inputText === '') return; // 仅处理非空搜索内容
    const currentTime = Date.now(); // 获取当前时间戳
    GM_setValue('inputText', inputText);
    GM_setValue('lastSearchTime', currentTime);
    switch (keyword) {
      case '豆包':
        window.open('https://www.doubao.com/chat/', '_blank');
        break;
      case 'ECNU':
        window.open('https://chat.ecnu.edu.cn/', '_blank');
        break;
      case 'DeepSeek':
        window.open('https://chat.deepseek.com/', '_blank');
        break;
    }
  }

  if (window.location.href.includes('http://127.0.0.1:6699/')) {
    // 获取网页A上的输入框和搜索按钮
    const inputElement = document.querySelector('#headMenuSearchInput');
    const website_name = document.querySelector('#searchChText');
    const searchButton = document.querySelector('#searchWB');

    // 监听搜索按钮点击事件
    searchButton.addEventListener('click', function (event) {
      if (website_name.innerText === '豆包') {
        scriptSearch('豆包', inputElement);
      }
      else if (website_name.innerText === 'ECNU') {
        scriptSearch('ECNU', inputElement);
      }
      else if (website_name.innerText === 'DeepSeek') {
        scriptSearch('DeepSeek', inputElement);
      }
    });

    // 监听回车键事件
    inputElement.addEventListener('keydown', function (event) {
      if (event.key === 'Enter') {
        if (website_name.innerText === '豆包') {
          scriptSearch('豆包', inputElement);
        }
        else if (website_name.innerText === 'ECNU') {
          scriptSearch('ECNU', inputElement);
        }
        else if (website_name.innerText === 'DeepSeek') {
          scriptSearch('DeepSeek', inputElement);
        }
      }
    });

    //监控每一个搜索记录的点击事件，延后确保页面加载完成
    let historyCnt = 0;
    let historyLock = setInterval(() => {
      if (historyCnt++ > 15) {
        clearInterval(historyLock);
      }
      if (document.getElementsByClassName('searchHistoryItem').length === 0) {
        return;
      }
      clearInterval(historyLock);
      const searchHistory = document.getElementsByClassName('searchHistoryItem');
      for (let i = 0; i < searchHistory.length; i++) {
        searchHistory[i].addEventListener('click', function (event) {
          if (website_name.innerText === '豆包') {
            scriptSearch('豆包', inputElement);
          }
          else if (website_name.innerText === 'ECNU') {
            scriptSearch('ECNU', inputElement);
          }
          else if (website_name.innerText === 'DeepSeek') {
            scriptSearch('DeepSeek', inputElement);
          }
        });
      }
    }, 500);
  }

  else if (window.location.href.includes('www.doubao.com/chat/')) {
    window.addEventListener('load', function () {
      const inputText = GM_getValue('inputText', '');
      const lastSearchTime = GM_getValue('lastSearchTime', 0);
      if (lastSearchTime == 0) {
        return;
      }
      const currentTime = Date.now();
      const timeDifference = currentTime - lastSearchTime;
      let cnt = 0;
      if (timeDifference <= 3000) {
        let lock = setInterval(() => {
          if (document.querySelector('.send-btn-wrapper') == null) {
            if (cnt++ > 15) {
              clearInterval(lock);
            }
            return;
          }
          clearInterval(lock);
          setTimeout(async () => {
            if (inputText !== '') {
              const textArea = document.querySelector('textarea');
              const simulator = new TextAreaSimulator(textArea);
              simulator.type(inputText + '\n');
            }
          }, 754);
        }, 500);
      }
      GM_deleteValue('inputText');
      GM_deleteValue('lastSearchTime');
    });
  }
  else if (window.location.href.includes('chat.ecnu.edu.cn')) {
    window.addEventListener('load', function () {
      const inputText = GM_getValue('inputText', '');
      const lastSearchTime = GM_getValue('lastSearchTime', 0);
      if (lastSearchTime == 0) {
        return;
      }
      const currentTime = Date.now();
      const timeDifference = currentTime - lastSearchTime;
      let cnt = 0;
      // 如果在3秒内，自动填写搜索框
      if (timeDifference <= 3000) {
        let lock = setInterval(() => {
          if (document.querySelector('.text-ellipsis') == null) {
            if (cnt++ > 15) {
              clearInterval(lock);
            }
            return;
          }
          clearInterval(lock);
          setTimeout(async () => {
            if (inputText !== '') {
              let target = document.getElementsByClassName("chat_chat-input-action__DMW7Y");
              target[target.length - 1].click();
              const textArea = document.querySelector('textarea');
              const simulator = new TextAreaSimulator(textArea);
              simulator.type(inputText + '\n');
            }
          }, 754);
        }, 500);
      }
      GM_deleteValue('inputText');
      GM_deleteValue('lastSearchTime');
    });
  }
  else if (window.location.href.includes('https://sso.ecnu.edu.cn/login?')) {
    window.addEventListener('load', function () {
      let cnt = 0;
      let lock = setInterval(() => {
        if (document.getElementsByClassName('ant-input').length !== 3 || document.getElementsByClassName('ant-input')[2].value == "") {
          if (cnt++ > 15) {
            clearInterval(lock);
          }
          return;
        }
        clearInterval(lock);
        const passwordInput = document.getElementsByClassName('ant-input')[1];
        const simulator = new InputSimulator(passwordInput);
        simulator.type('Zy9977553311');

        setTimeout(() => {
          document.getElementsByClassName('login-button')[0].click();
        }, 1400);
      }, 500);
    });
  }
  else if (window.location.href.includes("chat.deepseek.com/")) {
    window.addEventListener('load', function () {
      const inputText = GM_getValue('inputText', '');
      const lastSearchTime = GM_getValue('lastSearchTime', 0);
      if (lastSearchTime == 0) {
        return;
      }
      const currentTime = Date.now();
      const timeDifference = currentTime - lastSearchTime;
      let cnt = 0;
      if (timeDifference <= 3000) {
        let lock = setInterval(() => {
          if (document.querySelector('.ds-button') == null) {
            if (cnt++ > 15) {
              clearInterval(lock);
            }
            return;
          }
          clearInterval(lock);
          setTimeout(async () => {
            if (inputText !== '') {
              const textArea = document.querySelector('textarea');
              const simulator = new TextAreaSimulator(textArea);
              simulator.type(inputText + '\n');
            }
          }, 754);
        }, 500);
      }
      GM_deleteValue('inputText');
      GM_deleteValue('lastSearchTime');
    });
  }
})();
